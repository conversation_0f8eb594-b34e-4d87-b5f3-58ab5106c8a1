CREATE TABLE `area` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `area_no` int(11) DEFAULT NULL COMMENT '运营服务区编号，唯一标识一个运营服务区，如44268代表益禾堂荆州，1001代表杭州',
  `area_name` varchar(50) DEFAULT NULL COMMENT '运营服务区名称，如"益禾堂荆州"、"杭州"',
  `status` tinyint(1) DEFAULT '0' COMMENT '是否开放：0-不开放，1-开放',
  `origin_area_no` int(11) DEFAULT NULL COMMENT '代表复制自哪个运营服务区编号，为空则表示非复制出来的',  
  `large_area_no` int(11) DEFAULT NULL COMMENT '运营大区编号，如91表示荆州所属大区，1表示杭州所在的‘杭州大区’，关联`large_area`.`large_area_no`，大区名字为: `large_area`.`large_area_name`',
  `business_line` tinyint(4) NOT NULL DEFAULT '0' COMMENT '业务线：0-鲜沐，1-顺鹿达(POP)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_area_name` (`area_name`),
  UNIQUE KEY `index_area_no` (`area_no`),
  KEY `idx_large_area` (`large_area_no`)
) ENGINE=InnoDB AUTO_INCREMENT=360 DEFAULT CHARSET=utf8 COMMENT='运营服务区表，这是定价的基本单元，每个sku都是按照area_no来进行不同的定价的。';

-- 请注意，这里的运营服务区和城市并不是一一对应的，当用户提到XX市的客户时，请使用客户的注册城市(`merchant`.`city`)来匹配。