CREATE TABLE `after_sale_proof` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `after_sale_order_no` varchar(36) DEFAULT NULL COMMENT '售后订单号，关联after_sale_order.after_sale_order_no',
  `quantity` int(11) DEFAULT NULL COMMENT '售后数量',
  `proof_pic` varchar(1000) DEFAULT NULL COMMENT '售后凭证图片',
  `handle_num` decimal(10,2) DEFAULT NULL COMMENT '最终售后金额，客服已经审批完成了且同意的金额',
  `apply_remark` varchar(1000) DEFAULT NULL COMMENT '客服审核备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='售后明细表，记录售后单的处理明细情况';