CREATE TABLE `orders` (
  `order_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '订单ID，主键自增',
  `order_no` varchar(36) DEFAULT NULL COMMENT '订单编号，唯一标识订单的字符串',
  `m_id` bigint(30) DEFAULT NULL COMMENT '商户编号，关联商户表:merchant.m_id',
  `order_time` datetime DEFAULT NULL COMMENT '订单生成时间，记录下单时间',
  `type` int(11) DEFAULT '0' COMMENT '订单类型：0-普通（一次购买仅可一次性配送完），1-省心送（一次购买可分多次配送），2-运费，3-代下单，10-虚拟商品（黄金卡、充值等），11-直发采购，30-pop商城订单',
  `status` smallint(6) DEFAULT '1' COMMENT '订单状态：1-待支付，2-待配送，3-待收货，6-已收货，7-申请退款订单，8-已退款订单，9-支付失败订单，10-支付中断超时关闭订单，11-已撤销订单，14-手动关闭订单，15-人工退款中',
  `delivery_fee` decimal(12,2) DEFAULT '0.00' COMMENT '配送费用，记录订单的配送费用金额',
  `total_price` decimal(12,2) DEFAULT '0.00' COMMENT '总金额，订单商品总价。请注意，如果关联`order_item`表查询时，必须要使用`order_item`.`actual_total_price`来计算订单总价，否则会因为一笔订单有多个order_item而重复计算订单总额',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注，订单的附加说明信息',
  `confirm_time` datetime DEFAULT NULL COMMENT '确认收货时间，记录用户确认收货的时间',
  `out_times` int(2) DEFAULT '0' COMMENT '客户使用超时加单的次数。因我司每天有截单时间，过了截单时间下的订单，需要下下个配送周期才能配送。用户可用超时加单权益来实现过了截单时间但仍然享受及时的配送',
  `out_times_fee` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '客户使用超时加单权益时，所支付的费用。',
  `area_no` int(11) DEFAULT NULL COMMENT '商户下单时所属的区域编号，关联区域表:`area`.`area_no`',
  `m_size` varchar(20) DEFAULT NULL COMMENT '商户规模，取值范围[大客户、单店]',
  `account_id` bigint(30) DEFAULT NULL COMMENT '子账号ID，关联子账号表: `merchant_sub_account`.`account_id`，门店的子账户，门店可拥有多个子账户',
  `origin_price` decimal(10,2) DEFAULT NULL COMMENT '应付价格，订单原始价格',
  `receivable_status` smallint(6) NOT NULL DEFAULT '0' COMMENT '应收款状态：0-无应收款，1-未付款，2-部分付款，3-已付清',
  `admin_id` int(11) DEFAULT NULL COMMENT '门店所属的大客户ID，如果为NULL，则表示这是‘单店’类型的门店创建的订单。关联查询:`admin`.`admin_id` 且 admin_type = 0. 请注意这个字段不可用来判定私海关系，这个字段仅仅是表示这笔订单的门店是否从属于某个大客户。',
  `invoice_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发票状态：0-未开票，1-部分开票，2-已开票',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间，记录订单最后更新时间',
  `order_pay_type` tinyint(4) DEFAULT NULL COMMENT '代下单类型：1-账期，2-现结，3-账期代下，4-现结代下单',
  `selling_entity_name` varchar(30) NOT NULL DEFAULT '杭州鲜沐科技有限公司' COMMENT '销售主体名称，记录订单的销售主体',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `IN_orderno` (`order_no`,`status`),
  KEY `orders_ts_index` (`type`,`status`,`area_no`),
  KEY `rds_idx_2` (`status`,`m_id`,`order_time`,`area_no`),
  KEY `orders_area_no_index` (`area_no`,`status`),
  KEY `orders_time_index` (`order_time`,`status`),
  KEY `orders_sale_index` (`order_sale_type`,`status`,`area_no`),
  KEY `idx_financial_invoice_id` (`financial_invoice_id`),
  KEY `idx_mid` (`m_id`,`status`,`order_time`),
  KEY `idx_orderno_type_mid_areano` (`order_no`,`type`,`m_id`,`area_no`)
) ENGINE=InnoDB AUTO_INCREMENT=14838710 DEFAULT CHARSET=utf8 COMMENT='订单表，记录所有订单的基本信息，包括订单状态、金额、门店ID、门店所属的运营服务区、订单配送信息等'

CREATE TABLE `order_item` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '订单项ID，自增主键',
  `pd_name` varchar(255) DEFAULT NULL COMMENT '商品名称（快照），关联`products`.`pd_name`, 如：‘泰国椰青’、‘晴王青提(阳光玫瑰)’、‘TS韩国幼砂糖’',
  `sku` varchar(30) NOT NULL COMMENT '产品编号，唯一标识商品，关联`inventory`.`sku`如：5455443070、16823457512',
  `weight` varchar(100) DEFAULT NULL COMMENT '重量/规格，描述商品规格信息，如：毛重24-26斤/一级/特大果9个',
  `maturity` varchar(36) DEFAULT NULL COMMENT '生熟度，描述商品成熟状态',
  `order_no` varchar(36) DEFAULT NULL COMMENT '订单编号，关联订单表:orders.order_no，如：0125IX2SAE0328191411',
  `category_id` int(11) DEFAULT NULL COMMENT '商品分类ID, 关联 `category`.`category_id`',
  `amount` int(10) DEFAULT NULL COMMENT '购买数量，如：2、3',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '购买时的商品单价（实际单价，使用了优惠之后的单价），如：104.00、72.00',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '购买时的商品单价（原价），如：104.00、455.00',
  `add_time` datetime DEFAULT NULL COMMENT '购买时间，如：2025-03-28 19:14:36',
  `storage_location` tinyint(2) DEFAULT '0' COMMENT '仓储区域，0表示未指定，2表示特定仓储区',
  `status` int(11) DEFAULT '1' COMMENT '订单项状态：1待支付，2待配送，3待收货，6已收货，7申请退款订单，8已退款订单，9支付失败订单，10支付中断超时关闭订单，11已撤销订单，14手动关闭订单，15人工退款中',
  `volume` varchar(255) DEFAULT NULL COMMENT '体积，如：0.450*0.450*0.170',
  `weight_num` decimal(10,2) DEFAULT '0.00' COMMENT '重量(kg)，如：13.00、2.90',
  `use_coupon` tinyint(4) DEFAULT NULL COMMENT '是否用券，0否1是',
  `product_type` int(11) DEFAULT '0' COMMENT '商品类型：0普通商品，1赠品',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `actual_total_price` decimal(10,2) unsigned DEFAULT NULL COMMENT '订单项实付总价，如：208.00、216.00',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `in_order_no` (`order_no`,`suit_id`,`sku`),
  KEY `order_item_sku_index` (`sku`),
  KEY `idx_orderno_sku_weight_pdname` (`order_no`,`sku`,`weight`,`pd_name`)
) ENGINE=InnoDB AUTO_INCREMENT=33655113 DEFAULT CHARSET=utf8 COMMENT='订单商品明细表，记录订单中每个商品的具体信息，包括商品名称、规格、单价、数量、总价、状态等，用于订单管理和商品追溯。每个订单可能有多个订单项';

CREATE TABLE `inventory` (
  `inv_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '库存记录ID，自增主键',
  `sku` varchar(30) NOT NULL COMMENT '产品编号，唯一标识商品，如1120453602288',
  `pd_id` bigint(30) DEFAULT NULL COMMENT '商品ID，关联商品表:products.pd_id',
  `origin` varchar(100) DEFAULT NULL COMMENT '商品产地，如"中国"',
  `unit` varchar(5) DEFAULT NULL COMMENT '商品包装单位，如"箱"、"个"',
  `after_sale_unit` varchar(5) DEFAULT NULL COMMENT '商品的售后单位，如："袋"，"G"，"罐"',
  `maturity` varchar(36) DEFAULT NULL COMMENT '商品生熟度，如"生鲜"、"熟食"',
  `weight` varchar(100) DEFAULT NULL COMMENT '商品包装规格描述，如：1KG*10袋，900G*12罐',
  `outdated` int(11) DEFAULT '0' COMMENT 'SKU状态：-1上新中 0使用中 1已删除',
  `volume` varchar(255) DEFAULT NULL COMMENT '商品体积，格式"长*宽*高"，如"0.230*0.230*0.230"',
  `weight_num` decimal(10,2) DEFAULT NULL COMMENT '商品重量(kg)，如3.00',
  `type` int(11) DEFAULT NULL COMMENT '商品类型：0自营 1代仓',
  `sub_type` tinyint(4) DEFAULT NULL COMMENT '商品二级性质，1:自营-代销不入仓(俗称‘全品类’，货权为供应商，货物直接从供应商处配送至客户)、2:自营-代销入仓(俗称‘全品类’,货权为供应商，货物会先入库到我司仓库，再统一配送至客户门店)、3:自营-经销(我司自行采购、入库、销售)、4:代仓-代仓(客户自己的商品，我司仅代为操作出入库以及存储、配送)、5:顺鹿达商品(POP)',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `audit_status` int(11) DEFAULT '1' COMMENT '审核状态：0待审核 1通过 2拒绝',
  `sku_name` varchar(60) DEFAULT NULL COMMENT 'SKU名称, 比如‘’。这个字段比较少使用，在涉及到商品名字的问题中，应该尽可能使用`products`.`pd_name`来匹配。',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID',
  `video_url` varchar(256) DEFAULT '' COMMENT '商品视频链接',
  PRIMARY KEY (`inv_id`),
  KEY `IN_sku` (`sku`,`outdated`),
  KEY `FK_product_pd_id` (`pd_id`,`outdated`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品SKU信息表，记录商品SKU、名字、规格、SPU(pd_id)、是否自营(经销)/代仓/是否‘全品类’等核心信息。当用户提到‘全品类’时，指的就是sub_type in (1,2)的SKU。';

CREATE TABLE `products` (
  `pd_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '商品ID，主键，自增长',
  `category_id` int(11) DEFAULT NULL COMMENT '商品分类ID，关联category.id表',
  `pd_name` varchar(255) DEFAULT NULL COMMENT '商品名称，如"封口膜"',
  `create_time` datetime DEFAULT NULL COMMENT '商品上架时间',
  `expire_time` datetime DEFAULT NULL COMMENT '商品下架时间',
  `outdated` int(11) NOT NULL DEFAULT '0' COMMENT '商品状态：-1:上新中, 0:有效, 1:已删除',
  `storage_location` tinyint(4) DEFAULT '0' COMMENT '仓储区域：0-未分类 1-冷冻 2-冷藏 3-常温',
  `origin` int(11) DEFAULT NULL COMMENT '商品产地ID',
  `storage_method` varchar(255) DEFAULT NULL COMMENT '存储方式说明',
  `picture_path` varchar(1255) DEFAULT NULL COMMENT '商品主图URL',
  `quality_time` int(10) NOT NULL DEFAULT '0' COMMENT '保质期时长，如24表示24个月',
  `quality_time_unit` varchar(30) NOT NULL DEFAULT 'day' COMMENT '保质期单位，如"month"表示月',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `create_type` int(11) DEFAULT '0' COMMENT '上新类型：0-平台 1-大客户 2-其他',
  `real_name` varchar(50) DEFAULT NULL COMMENT '商品实物名称，如"锦凯塑料咖啡杯"',
  `audit_status` int(11) DEFAULT '1' COMMENT '审核状态：0-待上新 1-上新成功 2-上新失败',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  PRIMARY KEY (`pd_id`),
  KEY `products_to_category_fk` (`category_id`),
  KEY `products_to_brand_fk` (`brand_id`),
  KEY `products_pdname_index` (`pd_name`),
) ENGINE=InnoDB AUTO_INCREMENT=15534 DEFAULT CHARSET=utf8 COMMENT='SPU基础信息表，存储平台所有SPU的基本信息和状态。每个products(pd_id)可能有多个inventory(SKU)';

CREATE TABLE `category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品类ID，主键自增长。其他表中的category_id都指向此表的id',
  `parent_id` int(11) DEFAULT NULL COMMENT '父品类ID，关联自身，用于构建品类层级关系。一级类目的parent_id为null，二级类目的parent_id指向一个一级类目的id，三级类目的parent_id则指向一个二级类目的id，以此类推。在查找指定类目时，请按需多次关联category表，以获取完整的类目路径',
  `category` varchar(255) DEFAULT NULL COMMENT '品类名称，如"香瓜"、"水果马蹄"等',
  `outdated` tinyint(1) NOT NULL DEFAULT '0' COMMENT '标记位-过时的品类：0表示正常品类，1表示已过时/删除的品类',
  `icon` varchar(255) DEFAULT NULL COMMENT '品类图标URL，用于前端展示',
  `type` int(2) DEFAULT NULL COMMENT '品类类型：1-全部，2-乳制品，3-非乳制品，4-水果',
  PRIMARY KEY (`id`),
  KEY `idx_outdated` (`outdated`,`type`),
  KEY `idx_parent_id` (`parent_id`,`outdated`)
) ENGINE=InnoDB AUTO_INCREMENT=1360 DEFAULT CHARSET=utf8 COMMENT='商品品类表，用于存储商品分类信息，该表为多级分类结构，通过parent_id建立层级关系。type字段为大类，包含水果、乳制品等大类；outdated标记失效品类。如"不知火丑橘"属于水果类(type=4)，其父品类ID为1140'

CREATE TABLE `crm_bd_org` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `bd_id` bigint(20) NOT NULL COMMENT '销售代表ID，唯一标识一个销售人员，如1132198，取自`admin`.`admin_id`',
  `bd_name` varchar(255) NOT NULL COMMENT '销售代表姓名，如"龙菲", 取自`admin`.`admin_name`',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '上级ID，关联自身，用于构建销售团队层级关系。普通BD的上级为销售经理M1，销售经理M1的上级为大区经理M2，大区经理M2的上级为销售总监M3。员工的详细相信为`admin`表所包含，关联`admin`.`admin_id`',
  `parent_name` varchar(255) DEFAULT NULL COMMENT '上级姓名，如"苏伟"，取自`admin`.`admin_name`',
  `rank` int(11) NOT NULL COMMENT '销售员的职级，取值范围:[4,3,2,1]。4: BD(普通销售代表); 3: M1(销售经理M1，是销售代表的一线主管); 2: M2(销售高级经理M2，是区域主管，一线主管的主管); 1: M3(销售总监M3，区域经理的主管，为销售团队的最高级管理层)。',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bd_id_rank` (`bd_id`,`rank`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1403 DEFAULT CHARSET=utf8mb4 COMMENT='销售组织架构表，记录销售人员信息及其上下级关系，通过parent_id建立层级关系，用于管理销售团队的组织结构。'

CREATE TABLE `follow_up_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `m_id` bigint(30) DEFAULT NULL COMMENT '商户ID，关联商户表:merchant.m_id',
  `admin_id` int(11) DEFAULT NULL COMMENT '业务发展人员ID，记录跟进人，为销售代表的ID，取自`admin`.`admin_id`',
  `admin_name` varchar(36) DEFAULT NULL COMMENT '拜访人姓名，通常是销售代表的名字',
  `follow_up_way` varchar(50) DEFAULT NULL COMMENT '跟进方式，如电话、微信、有效拜访等等',
  `condition` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '拜访记录的商户详细描述，比如商户的生意如何，竞争对手有哪些，对平台的反馈等等',
  `add_time` datetime DEFAULT NULL COMMENT '拜访记录创建时间',
  `next_follow_time` date DEFAULT NULL COMMENT '计划下次拜访时间',
  `visit_objective` tinyint(4) DEFAULT '2' COMMENT '拜访目的:0拉新,1催月活,2客户维护,3拓品,4售后处理,5催省心送',
  `visit_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '拜访类型:0普通拜访,1陪访',
  `province` varchar(30) DEFAULT NULL COMMENT '被拜访的商户的省份名称,比如：广东、浙江、广西',
  `city` varchar(30) DEFAULT NULL COMMENT '被拜访的商户的城市名称，比如：杭州市、贵阳市',
  `area` varchar(30) DEFAULT NULL COMMENT '被拜访的商户的区县名称，比如：余杭区、丰泽区',
  `area_no` int(10) unsigned DEFAULT NULL COMMENT '被拜访的商户的运营服务区编号，关联`area`.`area_no`',
  `last_visit_time` datetime DEFAULT NULL COMMENT '上次拜访时间',
  `last_visit_record_id` int(11) DEFAULT NULL COMMENT '上次拜访记录ID',
  PRIMARY KEY (`id`),
  KEY `fure_fk_m_id` (`m_id`),
  KEY `rds_idx_3` (`status`,`m_id`,`add_time`),
  KEY `idx_admin_time_id` (`add_time`,`admin_id`),
  KEY `idx_province_city_area` (`province`,`city`,`area`),
  KEY `idx_area_no` (`area_no`,`add_time`)
) ENGINE=InnoDB AUTO_INCREMENT=3776755 DEFAULT CHARSET=utf8 COMMENT='商户拜访记录表，用于记录销售人员对商户的拜访、沟通、维护等跟进情况，包含拜访方式、拜访内容、客户反馈等信息。'

CREATE TABLE `follow_up_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `m_id` bigint(30) DEFAULT NULL COMMENT '商户ID，唯一标识一个商户',
  `admin_id` int(11) DEFAULT NULL COMMENT '所属的销售员ID(`admin`.`admin_id`，或者`crm_bd_org`.`bd_id`)，标识当前负责跟进该商户的销售员',
  `admin_name` varchar(255) DEFAULT NULL COMMENT '销售员的姓名',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间，记录该商户被分配给销售员的时间',
  `reassign` tinyint(1) DEFAULT '0' COMMENT '公私海标识，0表示私海客户，1表示公海客户',
  `last_follow_up_time` datetime DEFAULT NULL COMMENT '最近跟进时间，记录最后一次跟进该商户的时间',
  `reassign_time` datetime DEFAULT NULL COMMENT '重新指派时间，记录商户被重新指派的时间',
  `reason` varchar(100) DEFAULT NULL COMMENT '释放或跟进原因，描述为何释放或跟进该商户，比如‘新注册用户’，‘主管分配’',
  `follow_type` int(11) DEFAULT '0' COMMENT '新购买标签：0表示无，1表示新购买，2表示由定时任务处理取消新购买标签',
  `danger_day` int(11) DEFAULT NULL COMMENT '自动释放倒计时，表示距离自动释放的天数',
  `timing_follow_type` int(11) DEFAULT '0' COMMENT '省心送标签：0表示商户无未履约完的省心送订单，1表示有待履约的省心送订单',
  `province` varchar(30) DEFAULT NULL COMMENT '商户所在省份，如：安徽',
  `city` varchar(30) DEFAULT NULL COMMENT '商户所在城市，如：合肥市',
  `area` varchar(30) DEFAULT NULL COMMENT '商户所在区县，如：蜀山区',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录最后修改时间',
  `source` int(11) DEFAULT NULL COMMENT '上次所属的销售ID，0表示上次在公海',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_m_id` (`m_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商户和销售关系表(私海关系)，记录商户与我司销售员之间的关联关系。一个商户最多只能关联到一个销售，即只能处于一个销售的私海，处于私海的客户的下单金额算作销售的业绩，业绩是给销售提成的最重要考核指标。但是，商户可能不属于任意销售，这种情况我们认为商户处于公海，为公海客户。公海客户算作是M1销售经理的团队客户，业绩只能算做M1销售的业绩。';

CREATE TABLE `area` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `area_no` int(11) DEFAULT NULL COMMENT '运营服务区编号，唯一标识一个运营服务区，如44268代表益禾堂荆州，1001代表杭州',
  `area_name` varchar(50) DEFAULT NULL COMMENT '运营服务区名称，如"益禾堂荆州"、"杭州"',
  `status` tinyint(1) DEFAULT '0' COMMENT '是否开放：0-不开放，1-开放',
  `type` int(2) DEFAULT '1' COMMENT '仓库类型：0-本部仓，1-外部仓，2-合伙人仓',
  `map_section` varchar(1000) DEFAULT NULL COMMENT '截单映射区域，格式如"浙江/杭州市/西湖区"，多个区域用逗号分隔',
  `origin_area_no` int(11) DEFAULT NULL COMMENT '代表复制自哪个运营服务区编号，为空则表示非复制出来的',
  `administrative_area` varchar(50) DEFAULT NULL COMMENT '行政区域划分，精确到市，如"湖北/荆州市"',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `support_add_order` tinyint(4) DEFAULT NULL COMMENT '是否商户支持截单后加单：0-支持，1-不支持',
  `large_area_no` int(11) DEFAULT NULL COMMENT '运营大区编号，如91表示荆州所属大区，1表示杭州所在的‘杭州大区’，关联`large_area`.`large_area_no`，大区名字为: `large_area`.`large_area_name`',
  `grade` char(2) NOT NULL DEFAULT 'S' COMMENT '区域等级：S/A/B/C/D，S为最高级',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `business_line` tinyint(4) NOT NULL DEFAULT '0' COMMENT '业务线：0-鲜沐，1-POP',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_area_name` (`area_name`),
  UNIQUE KEY `index_area_no` (`area_no`),
  KEY `idx_parent` (`parent_no`),
  KEY `idx_large_area` (`large_area_no`)
) ENGINE=InnoDB AUTO_INCREMENT=360 DEFAULT CHARSET=utf8 COMMENT='运营服务区表，这是定价的基本单元，每个sku都是按照area_no来进行不同的定价的。该表存储全国各服务城市的基础信息，支持多级区域管理和业务线区分';

CREATE TABLE `admin` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_time` datetime NOT NULL COMMENT '创建时间，记录账号创建时间',
  `is_disabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否禁用：0-启用，1-禁用', 
  `username` varchar(255) DEFAULT NULL COMMENT '登录用户名，通常是邮箱',
  `login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `realname` varchar(255) DEFAULT NULL COMMENT '真实员工姓名（包括销售员和主管M1,M2,M3），比如“彭琨”；也可能是大客户的工商名称(此时admin_type必须要=0)，比如“四川茶语道企业管理有限公司”',
  `phone` varchar(18) DEFAULT NULL COMMENT '联系电话',
  `saler_id` int(11) DEFAULT NULL COMMENT '当本条记录是大客户时，该大客户所属的销售员admin_id',
  `saler_name` varchar(20) DEFAULT NULL COMMENT '当本条记录是大客户时，该大客户所属的销售员姓名',
  `name_remakes` varchar(50) DEFAULT NULL COMMENT '大客户在我司系统中的名称，大多数情况下都应该优先使用这个字段来查询。比如浙江星巴克、浙江茶百道、乐乐茶、霸王茶姬香水柠檬',
  `major_cycle` int(2) DEFAULT NULL COMMENT '报价周期：0-周报价，1-半月报价，2-月报价，3-日报价',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `admin_type` int(11) DEFAULT NULL COMMENT '用户类别：请注意，这个字段为NULL时，则代表这是本公司职员，包括[销售员、经理、后端]等等。0:代表这是我司的大客户（他们也可以登录我方系统），2-批发客户（他们也可以登录我方系统）',
  `admin_chain` int(11) DEFAULT NULL COMMENT '连锁范围：0-NKA(全国连锁)，1-LKA(区域连锁)，2-其他连锁',
  `admin_grade` int(11) DEFAULT NULL COMMENT '品牌等级：0-普通，1-KA',
  `admin_switch` int(11) DEFAULT '1' COMMENT '充送开关：0-开启，1-关闭',
  PRIMARY KEY (`admin_id`),
  KEY `idx_username` (`username`),
  KEY `idx_realname` (`realname`),
  KEY `idx_base_user_id` (`base_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='核心人员表。既存储了我司所有员工的信息，也存储的我司大客户的信息（因为我司的大客户也可登录我司后台），同时记录了大客户的销售员ID信息。';


CREATE TABLE `merchant` (
  `m_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '商户ID，代表一家门店的唯一ID',
  `mname` varchar(255) DEFAULT NULL COMMENT '商户名称，或者门店名称',
  `phone` varchar(20) DEFAULT NULL COMMENT '商户的手机，指注册时使用的手机号',
  `islock` int(11) DEFAULT '1' COMMENT '审核状态：0、审核通过 1、审核中 2、审核未通过 3、账号被拉黑 4、注销',
  `register_time` datetime DEFAULT NULL COMMENT '注册时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user` int(11) DEFAULT NULL COMMENT '审核人ID，取自`admin`.`admin_id`',
  `province` varchar(20) DEFAULT NULL COMMENT '商户的注册地址所在省份，比如【广东,广西壮族自治区,浙江】，请注意这个字段不会以‘省’结尾。',
  `city` varchar(20) DEFAULT NULL COMMENT '商户的注册地址所在市,比如【南宁市、宁波市】',
  `area` varchar(50) DEFAULT NULL COMMENT '商户的注册地址所在区县，比如【江南区，海曙区】',
  `address` varchar(255) DEFAULT NULL COMMENT '商户的详细地址，如：建政街道东葛路1055号众享披萨(青秀万达店)',
  `last_order_time` datetime DEFAULT NULL COMMENT '上次下单时间',
  `area_no` int(11) DEFAULT '1001' COMMENT '商户被划分到的运营服务区编号，取自`area`.`area_no`, 一个商户必定有一个唯一的area_no',
  `size` varchar(50) NOT NULL DEFAULT '单店' COMMENT '门店的规模：[大客户，大连锁，小连锁，单店]',
  `type` varchar(50) DEFAULT NULL COMMENT '客户类型:[其他,其它,加盟店,咖啡,水果/果切/榨汁店,水果店,水果捞/果切店,甜品冰淇淋,社区生鲜店,茶饮,菜市场水果摊,西餐,请选经营类型,面包蛋糕,面包蛋糕点心]',
  `admin_id` int(11) DEFAULT NULL COMMENT '当此字段不为空时，表示此商户所属的大客户ID。如果此字段为空，则表示这是一家单店，不属于任何连锁品牌。关联查询：admin.admin_id and admin_type = 0。',
  `direct` int(11) DEFAULT NULL COMMENT '是否支持账期，1：账期，2：现结（必须要现金结算货款）',
  `member_integral` decimal(10,2) DEFAULT '0.00' COMMENT '会员当月积分',
  `grade` int(2) DEFAULT NULL COMMENT '会员等级[0,1,2,3]',
  `recharge_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '鲜沐卡余额（一种预充值服务，门店需要先充值给我司，然后下单时可用鲜沐卡余额支付）',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `operate_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '商户经营状态。0:正常, 1:倒闭, 2:待提交核验, 3:核验中, 4:已核验拒绝(补充资料后可再次发起核验)',
  `business_line` int(11) DEFAULT '0' COMMENT '业务线，0=鲜沐; 1=pop(顺鹿达)',
  `submit_review_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '用户提交审核时间',
  PRIMARY KEY (`m_id`),
  UNIQUE KEY `Unique_openid` (`openid`) USING BTREE,
  UNIQUE KEY `i_phone` (`phone`),
  KEY `index_mname` (`mname`),
  KEY `merchant_admin_index` (`admin_id`,`direct`,`m_id`),
  KEY `merchant_area_index` (`area_no`,`size`,`type`) USING BTREE,
  KEY `merchant_islock_m_id_index` (`islock`,`admin_id`),
  KEY `idx_channel_code` (`channel_code`),
  KEY `idx_province_city_area` (`province`,`city`,`area`),
  KEY `idx_islock_register_time` (`islock`,`register_time`)
) ENGINE=InnoDB AUTO_INCREMENT=525609 DEFAULT CHARSET=utf8 COMMENT='商户信息主表。存储了商户的名字、注册地址以及省市区、手机号、所属的大客户admin_id（如有）、所属的运营服务区编号(area_no)等核心信息。一个门店有可能是连锁品牌的加盟店之一，此时它的admin_id字段应该不为空。admin_id为NULL时表示这是一个单店（独立门店）'


CREATE TABLE `merchant_sub_account` (
  `account_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '主键、自增',
  `m_id` bigint(30) DEFAULT NULL COMMENT '店铺id,关联查询`merchant`.`m_id`',
  `type` int(1) DEFAULT NULL COMMENT '子账号类型：0:母账号(代表这是店长)，1:子账号(通常来说是指商户店员)',
  `contact` varchar(255) DEFAULT NULL COMMENT '子账户名称',
  `phone` varchar(20) DEFAULT NULL COMMENT '子账户手机号',
  `status` int(11) DEFAULT '0' COMMENT '子账户是否审核通过（门店店长可审核子账户）：0:待审核, 1:审核通过, 2:已注销',
  `delete_flag` int(1) DEFAULT '1' COMMENT '删除标识：0、已删除 1、未删除',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`account_id`),
  KEY `merchant_sub_account_mid_index` (`m_id`),
  KEY `idx_phone` (`phone`),
) ENGINE=InnoDB AUTO_INCREMENT=576959 DEFAULT CHARSET=utf8 COMMENT='商户子账号主表。一个merchant至少有一个子账号，商户可能存在多个子账号。'

CREATE TABLE `shopping_cart` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `m_id` bigint(20) DEFAULT NULL COMMENT '门店ID, 关联查询`merchant`.`m_id`',
  `account_id` bigint(20) DEFAULT NULL COMMENT '子账号id，管理查询 `merchant_sub_account`.`account_id`',
  `biz_id` bigint(20) DEFAULT NULL COMMENT '业务id',
  `sku` varchar(50) DEFAULT NULL COMMENT 'sku, 关联查询`inventory`.`sku`',
  `parent_sku` varchar(50) DEFAULT NULL COMMENT '搭配购上级sku, 关联查询`inventory`.`sku`',
  `product_type` tinyint(4) DEFAULT NULL COMMENT '商品类型。0:普通商品，1:赠品，2:换购。',
  `quantity` int(11) DEFAULT NULL COMMENT '加购的商品数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_index` (`m_id`,`account_id`,`sku`,`product_type`,`parent_sku`) COMMENT '唯一索引',
  KEY `biz_id_and_sku_index` (`biz_id`,`sku`) COMMENT 'bizId、sku普通索引'
) ENGINE=InnoDB AUTO_INCREMENT=******** DEFAULT CHARSET=utf8 COMMENT='用户的购物车列表，一个门店(m_id)可能有多个子账户(account_id),每个子账户都有自己的购物车列表'

CREATE TABLE `after_sale_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
  `after_sale_order_no` varchar(36) NOT NULL COMMENT '售后订单编号，唯一标识售后申请',
  `m_id` bigint(30) DEFAULT NULL COMMENT '售后门店ID，关联merchant.m_id',
  `order_no` varchar(36) DEFAULT NULL COMMENT '原订单编号，关联orders.order_no',
  `sku` varchar(30) DEFAULT NULL COMMENT '售后商品SKU，为空表示整单售后, 关联inventory.sku',
  `status` int(2) DEFAULT NULL COMMENT '售后状态售后状态：0、审核中, 指待售后人员审核 1、审批中, 指售后人员已审核, 待售后主管审批中 2、成功 3、失败, 指审核人员拒绝了本次售后 4、补充凭证 指审核人员要求客户提供额外信息 11、取消, 指客户自己取消了本次售后',
  `after_sale_order_status` tinyint(4) DEFAULT '0' COMMENT '售后单类型：0 普通售后单，1 拦截售后单',
  `add_time` datetime DEFAULT NULL COMMENT '申请时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='售后单明细，记录订单的售后申请的基本信息.';

CREATE TABLE `after_sale_proof` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `after_sale_order_no` varchar(36) DEFAULT NULL COMMENT '售后订单号，关联after_sale_order.after_sale_order_no',
  `quantity` int(11) DEFAULT NULL COMMENT '售后数量',
  `proof_pic` varchar(1000) DEFAULT NULL COMMENT '售后凭证图片',
  `handle_num` decimal(10,2) DEFAULT NULL COMMENT '最终售后金额，客服已经审批完成了且同意的金额',
  `apply_remark` varchar(1000) DEFAULT NULL COMMENT '客服审核备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='售后明细表，记录售后单的处理明细情况';

