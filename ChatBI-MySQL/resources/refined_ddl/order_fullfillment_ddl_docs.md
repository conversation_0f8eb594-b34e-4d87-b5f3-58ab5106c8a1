CREATE TABLE `area_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `area_no` int(11) DEFAULT NULL COMMENT '库存仓号, 关联查询`warehouse_storage_center`.`warehouse_no`，库存仓名字则为:`warehouse_storage_center`.`warehouse_name`，可直接查询warehouse_storage_center获取库存仓名字和编号',
  `sku` varchar(30) DEFAULT NULL COMMENT 'sku编号, 关联查询`inventory`.`sku`',
  `quantity` int(10) unsigned DEFAULT '0' COMMENT '仓库内的实物库存数量（不等于可用库存，因为可能被客户的订单锁定了）',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `online_quantity` int(10) unsigned DEFAULT '0' COMMENT '线上库存，可用库存，当可用库存大于0时，才能被客户购买',
  `sale_lock_quantity` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '销售冻结库存量，指已经被订单占用了的库存，不可再用作它途',
  `lock_quantity` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '全部的锁定库存额，包含销售冻结(也就是sale_lock_quantity)、调拨冻结、采退冻结、盘点冻结等',
  `road_quantity` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '在途库存，采购或者调拨在送货途中的库存，还未入库',
  `status` int(2) DEFAULT '0' COMMENT 'sku的库内状态。0:正常, 1:采购入库中, 2:订单配送或者调拨等原因出入库中, 3:仓库盘点中',
  `advance_quantity` int(10) unsigned DEFAULT '0' COMMENT '采购预售库存',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户id(saas品牌方)，鲜沐为1',
  `warehouse_tenant_id` bigint(20) DEFAULT '1' COMMENT '仓库租户id(saas品牌方)，鲜沐为1',
  `owner_code` varchar(32) DEFAULT NULL COMMENT '货主编码',
  `owner_name` varchar(64) DEFAULT NULL COMMENT '货主名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_sku` (`area_no`,`sku`),
  KEY `area_store_sku_index` (`sku`),
  KEY `idx_wno_quantity` (`area_no`,`quantity`),
  KEY `idx_areano_onlinequantity` (`area_no`,`online_quantity`)
) ENGINE=InnoDB AUTO_INCREMENT=3491167 DEFAULT CHARSET=utf8 COMMENT='库存仓实际库存表，一个SKU在一个仓库只有一条库存记录'

CREATE TABLE `delivery_plan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(45) DEFAULT NULL COMMENT '订单号, 关联查询 `orders`.`order_no`',
  `status` smallint(6) DEFAULT '2' COMMENT '配送的订单的状态: 1待支付，2待配送，3待收货，6已收货，8已退款，10支付中断超时关闭订单，11已撤销，14手动关闭订单',
  `delivery_time` date DEFAULT NULL COMMENT '计划配送日，如：2025-04-04',
  `quantity` int(11) DEFAULT NULL COMMENT '本次的配送商品件数',
  `master_order_no` varchar(36) DEFAULT NULL COMMENT '随单配送时对应的普通订单订单号',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '配送计划更新时间',
  `deliverytype` tinyint(4) DEFAULT '0' COMMENT '配送方式：0:城配仓配送（车队），1:自提(客户到仓库自提)',
  `time_frame` varchar(50) DEFAULT NULL COMMENT '计划的预计送达时间区间（比如10:00～11:00）',
  `admin_id` int(11) DEFAULT NULL COMMENT '大客户ID，关联查询`admin`.`admin_id`且`admin_type`=0',
  `order_store_no` int(11) DEFAULT NULL COMMENT '下单时，库存扣减所用的库存仓编号，关联查询`warehouse_storage_center`.`warehouse_no`，库存仓名字则为:`warehouse_storage_center`.`warehouse_name`，可直接查询warehouse_storage_center获取库存仓名字和编号',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '配送计划生成时间',
  `put_off_time` date DEFAULT NULL COMMENT '省心送推迟配送时间，可能是因为缺货导致延迟配送',
  `old_delivery_time` date DEFAULT NULL COMMENT '原计划的配送时间',
  `intercept_flag` int(11) DEFAULT '0' COMMENT '是否被仓库端拦截. 0:正常, 1:被拦截',
  `intercept_time` datetime DEFAULT NULL COMMENT '拦截时间',
  PRIMARY KEY (`id`),
  KEY `ind_order_no` (`order_no`),
  KEY `ind_master_order_no` (`master_order_no`),
  KEY `ind_delivery_time` (`delivery_time`,`status`,`order_store_no`),
  KEY `ind_time_contact` (`contact_id`,`delivery_time`),
  KEY `ind_order_store_no_status` (`order_store_no`,`status`),
  KEY `idx_put_off_time` (`put_off_time`),
  KEY `idx_status_deliverytime` (`status`,`delivery_time`)
) ENGINE=InnoDB AUTO_INCREMENT=15402484 DEFAULT CHARSET=utf8 COMMENT='订单的配送计划表，一笔合法的订单可能有一个或者多个配送计划，仅当订单类型为省心送时，才会有多个配送计划'

CREATE TABLE `orders` (
  `order_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '订单ID，主键自增',
  `order_no` varchar(36) DEFAULT NULL COMMENT '订单编号，唯一标识订单的字符串',
  `m_id` bigint(30) DEFAULT NULL COMMENT '商户编号，关联商户表:merchant.m_id',
  `order_time` datetime DEFAULT NULL COMMENT '订单生成时间，记录下单时间',
  `type` int(11) DEFAULT '0' COMMENT '订单类型：0-普通（一次购买仅可一次性配送完），1-省心送（一次购买可分多次配送），2-运费，3-代下单，10-虚拟商品（黄金卡、充值等），11-直发采购，30-pop商城订单',
  `status` smallint(6) DEFAULT '1' COMMENT '订单状态：1-待支付，2-待配送，3-待收货，6-已收货，7-申请退款订单，8-已退款订单，9-支付失败订单，10-支付中断超时关闭订单，11-已撤销订单，14-手动关闭订单，15-人工退款中',
  `delivery_fee` decimal(12,2) DEFAULT '0.00' COMMENT '配送费用，记录订单的配送费用金额',
  `total_price` decimal(12,2) DEFAULT '0.00' COMMENT '总金额，订单商品总价。请注意，如果关联`order_item`表查询时，必须要使用`order_item`.`actual_total_price`来计算订单总价，否则会因为一笔订单有多个order_item而重复计算订单总额',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注，订单的附加说明信息',
  `confirm_time` datetime DEFAULT NULL COMMENT '确认收货时间，记录用户确认收货的时间',
  `out_times` int(2) DEFAULT '0' COMMENT '客户使用超时加单的次数。因我司每天有截单时间，过了截单时间下的订单，需要下下个配送周期才能配送。用户可用超时加单权益来实现过了截单时间但仍然享受及时的配送',
  `discount_type` int(11) NOT NULL DEFAULT '0' COMMENT '优惠类型：0-无优惠，1-优惠券，2-满减，3-满返',
  `out_times_fee` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '客户使用超时加单权益时，所支付的费用。',
  `area_no` int(11) DEFAULT NULL COMMENT '商户下单时所属的区域编号，关联区域表:`area`.`area_no`',
  `m_size` varchar(20) DEFAULT NULL COMMENT '商户规模，取值范围[大客户、单店]',
  `card_rule_id` int(11) DEFAULT NULL COMMENT '待发放优惠卡ID，关联优惠卡表',
  `account_id` bigint(30) DEFAULT NULL COMMENT '子账号ID，关联子账号表，门店的子账户，门店可拥有多个子账户',
  `origin_price` decimal(10,2) DEFAULT NULL COMMENT '应付价格，订单原始价格',
  `out_stock` int(11) DEFAULT '0' COMMENT '出库状态：0-未出库，1-已出库',
  `discount_card_id` int(11) DEFAULT NULL COMMENT '虚拟商品ID，如奶油卡ID、充值送券ID',
  `order_sale_type` int(11) DEFAULT '0' COMMENT '订单扩展类型：0-普通，1-预售，20-拼团订单',
  `receivable_status` smallint(6) NOT NULL DEFAULT '0' COMMENT '应收款状态：0-无应收款，1-未付款，2-部分付款，3-已付清',
  `admin_id` int(11) DEFAULT NULL COMMENT '大客户ID，关联大客户表:admin.admin_id 且admin_type = 0',
  `invoice_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发票状态：0-未开票，1-部分开票，2-已开票',
  `financial_invoice_id` bigint(20) DEFAULT NULL COMMENT '财务发票表ID（已弃用）',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间，记录订单最后更新时间',
  `operate_id` int(11) DEFAULT NULL COMMENT '代下单操作人ID，关联admin表，且admin_type = 1(表示这是公司员工账户ID)',
  `order_pay_type` tinyint(4) DEFAULT NULL COMMENT '代下单类型：1-账期，2-现结，3-账期代下，4-现结代下单',
  `selling_entity_name` varchar(30) NOT NULL DEFAULT '杭州鲜沐科技有限公司' COMMENT '销售主体名称，记录订单的销售主体',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `IN_orderno` (`order_no`,`status`),
  KEY `orders_ts_index` (`type`,`status`,`area_no`),
  KEY `rds_idx_2` (`status`,`m_id`,`order_time`,`area_no`),
  KEY `orders_area_no_index` (`area_no`,`status`),
  KEY `orders_time_index` (`order_time`,`status`),
  KEY `orders_sale_index` (`order_sale_type`,`status`,`area_no`),
  KEY `idx_financial_invoice_id` (`financial_invoice_id`),
  KEY `idx_mid` (`m_id`,`status`,`order_time`),
  KEY `idx_orderno_type_mid_areano` (`order_no`,`type`,`m_id`,`area_no`)
) ENGINE=InnoDB AUTO_INCREMENT=14838710 DEFAULT CHARSET=utf8 COMMENT='订单表，记录所有订单的基本信息，包括订单状态、金额、门店ID、门店所属的运营服务区、订单配送信息等'

CREATE TABLE `order_item` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '订单项ID，自增主键',
  `pd_name` varchar(255) DEFAULT NULL COMMENT '商品名称（快照），关联`products`.`pd_name`, 如：泰国椰青、晴王青提(阳光玫瑰)',
  `sku` varchar(30) NOT NULL COMMENT '产品编号，唯一标识商品，关联`inventory`.`sku`如：5455443070、16823457512',
  `weight` varchar(100) DEFAULT NULL COMMENT '重量/规格，描述商品规格信息，如：毛重24-26斤/一级/特大果9个',
  `maturity` varchar(36) DEFAULT NULL COMMENT '生熟度，描述商品成熟状态',
  `order_no` varchar(36) DEFAULT NULL COMMENT '订单编号，关联订单表:orders.order_no，如：0125IX2SAE0328191411',
  `category_id` int(11) DEFAULT NULL COMMENT '商品分类ID, 关联 `category`.`category_id`',
  `amount` int(10) DEFAULT NULL COMMENT '购买数量，如：2、3',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '购买时的商品单价（实际单价，使用了优惠之后的单价），如：104.00、72.00',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '购买时的商品单价（原价），如：104.00、455.00',
  `add_time` datetime DEFAULT NULL COMMENT '购买时间，如：2025-03-28 19:14:36',
  `storage_location` tinyint(2) DEFAULT '0' COMMENT '仓储区域，0表示未指定，2表示特定仓储区',
  `status` int(11) DEFAULT '1' COMMENT '订单项状态：1待支付，2待配送，3待收货，6已收货，7申请退款订单，8已退款订单，9支付失败订单，10支付中断超时关闭订单，11已撤销订单，14手动关闭订单，15人工退款中',
  `volume` varchar(255) DEFAULT NULL COMMENT '体积，如：0.450*0.450*0.170',
  `weight_num` decimal(10,2) DEFAULT '0.00' COMMENT '重量(kg)，如：13.00、2.90',
  `use_coupon` tinyint(4) DEFAULT NULL COMMENT '是否用券，0否1是',
  `product_type` int(11) DEFAULT '0' COMMENT '商品类型：0普通商品，1赠品',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `actual_total_price` decimal(10,2) unsigned DEFAULT NULL COMMENT '订单项实付总价，如：208.00、216.00',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `sku_name` varchar(60) DEFAULT NULL COMMENT 'sku名称，关联`inventory`.`sku_name`',
  `info` text COMMENT '下单快照信息（商品标签，商品有效期），如：{"validity":"2024-11-05 - 2025-08-02"}',
  PRIMARY KEY (`id`),
  KEY `in_order_no` (`order_no`,`suit_id`,`sku`),
  KEY `order_item_sku_index` (`sku`),
  KEY `idx_orderno_sku_weight_pdname` (`order_no`,`sku`,`weight`,`pd_name`)
) ENGINE=InnoDB AUTO_INCREMENT=33655113 DEFAULT CHARSET=utf8 COMMENT='订单商品明细表，记录订单中每个商品的具体信息，包括商品名称、规格、单价、数量、总价、状态等，用于订单管理和商品追溯。每个订单可能有多个订单项';

CREATE TABLE `products` (
  `pd_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '商品ID，主键，自增长',
  `category_id` int(11) DEFAULT NULL COMMENT '商品类目ID，关联category.id',
  `pd_name` varchar(255) DEFAULT NULL COMMENT '商品名称，如"封口膜"，“安佳淡奶油”',
  `pddetail` text COMMENT '商品详细描述信息',
  `after_sale_time` int(3) DEFAULT '38' COMMENT '售后有效期时长，如48表示48小时',
  `after_sale_type` varchar(50) DEFAULT NULL COMMENT '支持的售后类型，多个用分号分隔，如"商品数量不符;包装问题"',
  `after_sale_unit` varchar(5) DEFAULT NULL COMMENT '售后单位，如颗、件、箱、盒等',
  `create_time` datetime DEFAULT NULL COMMENT '商品上架时间',
  `expire_time` datetime DEFAULT NULL COMMENT '商品下架时间',
  `outdated` int(11) NOT NULL DEFAULT '0' COMMENT '商品状态：-1-上新中 0-有效 1-已删除',
  `storage_location` tinyint(4) DEFAULT '0' COMMENT '仓储区域：0-未分类 1-冷冻 2-冷藏 3-常温',
  `pd_no` varchar(30) DEFAULT NULL COMMENT '商品编号，如"621307756"',
  `storage_method` varchar(255) DEFAULT NULL COMMENT '存储方式说明',
  `slogan` varchar(36) DEFAULT NULL COMMENT '商品标语',
  `quality_time` int(10) NOT NULL DEFAULT '0' COMMENT '保质期时长，如24表示24个月',
  `quality_time_unit` varchar(30) NOT NULL DEFAULT 'day' COMMENT '保质期单位，如"month"表示月',
  `warn_time` int(10) DEFAULT NULL COMMENT '临保预警时长，如5表示提前5天预警',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `create_type` int(11) DEFAULT '0' COMMENT '上新类型：0-平台 1-大客户 2-其他',
  `real_name` varchar(50) DEFAULT NULL COMMENT '商品实物名称，如"锦凯塑料咖啡杯"',
  `quality_time_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '保质期类型：0-固定时长 1-到期时间',
  PRIMARY KEY (`pd_id`),
  KEY `products_to_category_fk` (`category_id`),
  KEY `products_to_brand_fk` (`brand_id`),
  KEY `products_pdname_index` (`pd_name`),
  CONSTRAINT `products_to_category_fk` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15534 DEFAULT CHARSET=utf8 COMMENT='SPU基础信息表，存储平台所有SPU的基本信息和状态。每个products(pd_id)可能有多个inventory(SKU)';


CREATE TABLE `inventory` (
  `inv_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '库存记录ID，自增主键',
  `sku` varchar(30) NOT NULL COMMENT '产品编号，唯一标识商品，如1120453602288',
  `store_quantity` int(10) DEFAULT '0' COMMENT '仓库实际库存数量，如0表示无库存',
  `limited_quantity` int(10) DEFAULT '-1' COMMENT '限制购买数量，-1表示无限制',
  `ait_id` int(10) DEFAULT NULL COMMENT '属性ID，关联商品属性表',
  `pd_id` bigint(30) DEFAULT NULL COMMENT '商品ID，关联商品表:products.pd_id',
  `origin` varchar(100) DEFAULT NULL COMMENT '商品产地，如"中国"',
  `unit` varchar(5) DEFAULT NULL COMMENT '商品单位，如"箱"、"个"',
  `maturity` varchar(36) DEFAULT NULL COMMENT '商品生熟度，如"生鲜"、"熟食"',
  `weight` varchar(100) DEFAULT NULL COMMENT '商品重量描述，如"100个*10条/98mm/98直饮盖"',
  `production_date` date DEFAULT NULL COMMENT '生产日期',
  `outdated` int(11) DEFAULT '0' COMMENT 'SKU状态：-1上新中 0使用中 1已删除',
  `volume` varchar(255) DEFAULT NULL COMMENT '商品体积，格式"长*宽*高"，如"0.230*0.230*0.230"',
  `weight_num` decimal(10,2) DEFAULT NULL COMMENT '商品重量(kg)，如3.00',
  `type` int(11) DEFAULT NULL COMMENT '商品类型：0自营 1代仓',
  `sub_type` tinyint(4) DEFAULT NULL COMMENT '商品二级性质，1:自营-代销不入仓、2:自营-代销入仓、3:自营-经销、4:代仓-代仓、5:顺鹿达商品(POP)',
  `sku_pic` varchar(255) DEFAULT NULL COMMENT 'SKU图片URL',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `audit_status` int(11) DEFAULT '1' COMMENT '审核状态：0待审核 1通过 2拒绝',
  `sku_name` varchar(60) DEFAULT NULL COMMENT 'SKU名称',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID',
  `video_url` varchar(256) DEFAULT '' COMMENT '商品视频链接',
  PRIMARY KEY (`inv_id`),
  KEY `IN_sku` (`sku`,`outdated`),
  KEY `FK_product_pd_id` (`pd_id`,`outdated`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品SKU库存表，记录商品SKU、名字、规格、SPU(pd_id)、是否自营/代仓/经销/代销等核心信息';

CREATE TABLE `warehouse_storage_center` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键、自增',
  `warehouse_no` int(11) DEFAULT NULL COMMENT '仓库编号，比如：10',
  `warehouse_name` varchar(50) DEFAULT NULL COMMENT '库存仓名称，比如：嘉兴总仓、东莞冷冻总仓、南京总仓等',
  `type` int(11) DEFAULT NULL COMMENT '仓库类型：0、本部仓 1、外部仓 2、合伙人仓',
  `status` int(11) DEFAULT '1' COMMENT '开放状态：0、不开放 1、开放',
  `address` varchar(255) DEFAULT NULL COMMENT '仓库所在的具体地址',
  PRIMARY KEY (`id`),
  UNIQUE KEY `warehouse_storage_center_warehouse_no_uindex` (`warehouse_no`),
  KEY `idx_warehouse_no_status` (`warehouse_no`,`status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=330 DEFAULT CHARSET=utf8 COMMENT='库存仓基本信息，包含了库存仓名字、编码、以及地理位置等'

CREATE TABLE `area` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `area_no` int(11) DEFAULT NULL COMMENT '运营服务区编号，唯一标识一个运营服务区，如44268代表益禾堂荆州，1001代表杭州',
  `area_name` varchar(50) DEFAULT NULL COMMENT '运营服务区名称，如"益禾堂荆州"、"杭州"',
  `status` tinyint(1) DEFAULT '0' COMMENT '是否开放：0-不开放，1-开放',
  `type` int(2) DEFAULT '1' COMMENT '仓库类型：0-本部仓，1-外部仓，2-合伙人仓',
  `map_section` varchar(1000) DEFAULT NULL COMMENT '截单映射区域，格式如"浙江/杭州市/西湖区"，多个区域用逗号分隔',
  `origin_area_no` int(11) DEFAULT NULL COMMENT '代表复制自哪个运营服务区编号，为空则表示非复制出来的',
  `administrative_area` varchar(50) DEFAULT NULL COMMENT '行政区域划分，精确到市，如"湖北/荆州市"',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `support_add_order` tinyint(4) DEFAULT NULL COMMENT '是否商户支持截单后加单：0-支持，1-不支持',
  `large_area_no` int(11) DEFAULT NULL COMMENT '运营大区编号，如91表示荆州所属大区，1表示杭州所在的‘杭州大区’，关联`large_area`.`large_area_no`，大区名字为: `large_area`.`large_area_name`',
  `grade` char(2) NOT NULL DEFAULT 'S' COMMENT '区域等级：S/A/B/C/D，S为最高级',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `business_line` tinyint(4) NOT NULL DEFAULT '0' COMMENT '业务线：0-鲜沐，1-POP',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_area_name` (`area_name`),
  UNIQUE KEY `index_area_no` (`area_no`),
  KEY `idx_parent` (`parent_no`),
  KEY `idx_large_area` (`large_area_no`)
) ENGINE=InnoDB AUTO_INCREMENT=360 DEFAULT CHARSET=utf8 COMMENT='运营服务区表。注意这个表和库存仓没有直接的关系！！这是定价的基本单元，每个sku都是按照area_no来进行不同的定价的。该表存储全国各服务城市的基础信息，支持多级区域管理和业务线区分';

CREATE TABLE `admin` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_time` datetime NOT NULL COMMENT '创建时间，记录账号创建时间',
  `is_disabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否禁用：0-启用，1-禁用', 
  `username` varchar(255) DEFAULT NULL COMMENT '登录用户名，通常是邮箱',
  `login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `realname` varchar(255) DEFAULT NULL COMMENT '真实员工姓名（包括销售员和主管M1,M2,M3），或者大客户的公司名称',
  `phone` varchar(18) DEFAULT NULL COMMENT '联系电话',
  `saler_id` int(11) DEFAULT NULL COMMENT '当本条记录是大客户时，该大客户所属的销售员admin_id',
  `saler_name` varchar(20) DEFAULT NULL COMMENT '当本条记录是大客户时，该大客户所属的销售员姓名',
  `name_remakes` varchar(50) DEFAULT NULL COMMENT '大客户的品牌名称，比如浙江星巴克、浙江茶百道、乐乐茶等',
  `major_cycle` int(2) DEFAULT NULL COMMENT '报价周期：0-周报价，1-半月报价，2-月报价，3-日报价',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `admin_type` int(11) DEFAULT NULL COMMENT '客户类别：0-大客户，1-本公司职员，包括销售员、经理、技术员等等，2-批发客户',
  `admin_chain` int(11) DEFAULT NULL COMMENT '连锁范围：0-NKA(全国连锁)，1-LKA(区域连锁)，2-其他连锁',
  `admin_grade` int(11) DEFAULT NULL COMMENT '品牌等级：0-普通，1-KA',
  `admin_switch` int(11) DEFAULT '1' COMMENT '充送开关：0-开启，1-关闭',
  PRIMARY KEY (`admin_id`),
  KEY `idx_username` (`username`),
  KEY `idx_realname` (`realname`),
  KEY `idx_base_user_id` (`base_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='核心人员表。既存储了我司所有员工的信息，也存储的我司大客户的信息（因为我司的大客户也可登录我司后台），同时记录了大客户的销售员ID信息。';


CREATE TABLE `merchant` (
  `m_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '商户ID，代表一家门店的唯一ID',
  `mname` varchar(255) DEFAULT NULL COMMENT '商户名称，或者门店名称',
  `phone` varchar(20) DEFAULT NULL COMMENT '商户的手机，指注册时使用的手机号',
  `islock` int(11) DEFAULT '1' COMMENT '审核状态：0、审核通过 1、审核中 2、审核未通过 3、账号被拉黑 4、注销',
  `register_time` datetime DEFAULT NULL COMMENT '注册时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `province` varchar(20) DEFAULT NULL COMMENT '商户的注册地址：省，比如广东,广西壮族自治区',
  `city` varchar(20) DEFAULT NULL COMMENT '商户的注册地址：市,比如南宁市、宁波市',
  `area` varchar(50) DEFAULT NULL COMMENT '商户的注册地址：区县，比如江南区，海曙区',
  `last_order_time` datetime DEFAULT NULL COMMENT '上次下单时间',
  `area_no` int(11) DEFAULT '1001' COMMENT '商户被划分到的运营服务区编号，取自`area`.`area_no`, 一个商户必定有一个唯一的area_no',
  `size` varchar(50) NOT NULL DEFAULT '单店' COMMENT '门店的规模：[大客户，大连锁，小连锁，单店]',
  `type` varchar(50) DEFAULT NULL COMMENT '客户类型:[其他,其它,加盟店,咖啡,水果/果切/榨汁店,水果店,水果捞/果切店,甜品冰淇淋,社区生鲜店,茶饮,菜市场水果摊,西餐,请选经营类型,面包蛋糕,面包蛋糕点心]',
  `admin_id` int(11) DEFAULT NULL COMMENT '门店所属的大客户ID，取自`admin`.`admin_id`，且admin_type=0。同时，门店可能是单店，不属于任何大客户，此时该字段为NULL',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `operate_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '运营状态:正常(0),倒闭(1)，待提交核验（2），待核验（3），核验拒绝（4）',
  `business_line` int(11) DEFAULT '0' COMMENT '业务线0=鲜沐;1=pop',
  `submit_review_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '用户提交审核时间',
  PRIMARY KEY (`m_id`),
  UNIQUE KEY `Unique_openid` (`openid`) USING BTREE,
  UNIQUE KEY `i_phone` (`phone`),
  KEY `index_mname` (`mname`),
  KEY `merchant_admin_index` (`admin_id`,`direct`,`m_id`),
  KEY `merchant_area_index` (`area_no`,`size`,`type`) USING BTREE,
  KEY `merchant_islock_m_id_index` (`islock`,`admin_id`),
  KEY `idx_channel_code` (`channel_code`),
  KEY `idx_province_city_area` (`province`,`city`,`area`),
  KEY `idx_islock_register_time` (`islock`,`register_time`)
) ENGINE=InnoDB AUTO_INCREMENT=525609 DEFAULT CHARSET=utf8 COMMENT='商户信息主表。存储了商户的名字、注册地址以及省市区、手机号、所属的大客户admin_id（如有）、所属的运营服务区编号(area_no)等核心信息'
