CREATE TABLE `purchase_replenishment_order` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `pd_id` bigint(20) NOT NULL COMMENT '商品spu_id products表中的pd_id字段',
  `warehouse_no` int(11) NOT NULL COMMENT '仓库编号 warehouse_storage_center表中的warehosue_no字段',
  `sku_id` varchar(30) NOT NULL COMMENT 'SKU编码 inventory表中的sku字段',
  `view_date` datetime NOT NULL COMMENT '日期，任务创建日期',
  `order_status` int(11) NOT NULL COMMENT '补货单状态1:待确认,2:已发起,3:已关闭,4:自动关闭',
  `supplier_id` int(11) NOT NULL COMMENT '供应商ID，supplier表中ID字段',
  `admin_id` int(11) NOT NULL COMMENT '采购负责人ID',
  `final_replenishment_quantity` int(11) DEFAULT NULL COMMENT '最终补货量',
  `final_supplier_id` int(11) NOT NULL COMMENT '最终供应商ID，supplier表中ID字段',
  `final_supplier_name` varchar(64) NOT NULL COMMENT '最终供应商名称姓名，supplier表中name字段快照',
  `final_admin_id` int(11) NOT NULL COMMENT '最终采购负责人id',
  `final_admin_name` varchar(32) NOT NULL COMMENT '最终采购负责人姓名',
  `relation_type` int(11) DEFAULT NULL COMMENT '关联操作类型：1:采购单',
  `relation_id` varchar(30) DEFAULT NULL COMMENT '关联操作id',
  `create_date` int(11) NOT NULL COMMENT '创建日期',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updater` int(11) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `creator` int(11) DEFAULT NULL COMMENT '创建人',
  `del_flag` int(11) NOT NULL COMMENT '删除标记 0:未删除 1:已删除',
  `source` tinyint(4) DEFAULT '0' COMMENT '0:缺货提醒 1:补货计划',
  `replenishment_plan_purchase_task_id` bigint(20) DEFAULT NULL COMMENT '补货任务id，补货计划中的任务id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_date` (`create_date`,`view_date`) USING BTREE,
  KEY `idx_warehouse_no` (`warehouse_no`) USING BTREE,
  KEY `idx_order_status` (`order_status`) USING BTREE,
  KEY `idx_sku_id` (`sku_id`) USING BTREE,
  KEY `idx_pd_name` (`pd_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=451 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='采购任务表-采购单前置确定的计划单据';

CREATE TABLE `purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'purchase',
  `purchase_no` varchar(30) DEFAULT NULL COMMENT '采购单号',
  `purchase_time` datetime DEFAULT NULL COMMENT '采购时间',
  `purchaser` varchar(15) DEFAULT NULL COMMENT '采购负责人（xianmu:adminName;cosfo：TenantAcountName）',
  `receiver` varchar(15) DEFAULT NULL COMMENT '收货负责人',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `state` int(11) DEFAULT '0' COMMENT '采购单状态： -1、作废 0、计划制定 1、已发布2、待供应商确认 3、审核中 4、审核拒绝',
  `area_no` int(11) DEFAULT NULL COMMENT '仓库编号',
  `delivery_type` int(2) DEFAULT '1' COMMENT ' 1送货到库，2自提',
  `remark` varchar(50) DEFAULT NULL COMMENT '备注',
  `purchases_type` tinyint(4) DEFAULT NULL COMMENT '0表示正常采购，1表示直发采购',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` int(11) DEFAULT NULL COMMENT '采购单状态(发票)： 0、未归档 1、已归档',
  `is_arrange` tinyint(4) DEFAULT '0' COMMENT '可预约标识：0 不可预约1 可预约',
  `process_state` tinyint(4) DEFAULT '0' COMMENT '入库进度：0、待入库 1、部分入库 2、已入库',
  `arrange_time` date DEFAULT NULL COMMENT '计划制定状态下预约入库时间',
  `arrange_remark` varchar(100) DEFAULT NULL COMMENT '计划制定状态下预约备注',
  `operator_type` tinyint(4) DEFAULT NULL COMMENT '供应商确认状态0未确认1已确认2已拒绝',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID(auth的bizid，鲜沐为admin_id,cosfo不确定)',
  `source` varchar(255) NOT NULL DEFAULT 'xianmu' COMMENT '来源:xianmu,saas',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户id',
  `delivery_time` datetime DEFAULT NULL COMMENT '（计划制定下）物流用车时间',
  `tms_dist_site_id` bigint(20) DEFAULT NULL COMMENT '（计划制定下）物流用车发货地址',
  `business_type` tinyint(4) DEFAULT NULL COMMENT '业务类型：1-代销不入库类型 2-代销不入仓-备货 3-代销不入仓-预提 4-POP 5-库存初始化',
  `take_time` datetime DEFAULT NULL COMMENT '提货时间',
  `pso_no` varchar(255) DEFAULT NULL COMMENT 'ofc采购供应单号',
  `origin_system` varchar(255) DEFAULT NULL COMMENT '来源系统(SCP_SYSTEM:scp计划)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `no_index` (`purchase_no`) USING BTREE,
  KEY `area_no_index` (`area_no`) USING BTREE,
  KEY `time_index` (`purchase_time`) USING BTREE,
  KEY `state_idx` (`tenant_id`,`state`),
  KEY `idx_pso` (`pso_no`(64))
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='采购单主表';

-- xianmudb.purchases_plan definition

CREATE TABLE `purchases_plan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_no` varchar(30) DEFAULT NULL COMMENT '采购单号(purchases表purchase_no字段)',
  `sku` varchar(30) DEFAULT NULL COMMENT '产品编号（sku编号，关联inventory表的sku字段）',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '采购总价',
  `quantity` int(11) DEFAULT NULL COMMENT '数量',
  `supplier_id` int(11) DEFAULT NULL COMMENT '供应商，supplier表中ID字段',
  `quality_date` date DEFAULT NULL COMMENT '货品保质期',
  `production_date` date DEFAULT NULL COMMENT '生产日期',
  `settle_flag` int(11) DEFAULT '0' COMMENT '（预付单用到了）采购项结算标识 0、可结算 1、不可结算（结算中或已完成）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `plan_status` int(11) DEFAULT '1' COMMENT '0作废数据，1：有效数据',
  `arrange_quantity` int(10) unsigned DEFAULT '0' COMMENT '可预约数量',
  `price_type` tinyint(4) DEFAULT '0' COMMENT '价格类型：0指定价1报价单 2 询竞价',
  `latest_arrival_date` date DEFAULT NULL COMMENT '最晚到货日期',
  `tax_rate` decimal(10,2) DEFAULT NULL COMMENT '税率',
  `pop_feature` text COMMENT 'pop拓展字段',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `purchases_plan_suppiler_index` (`supplier_id`) USING BTREE,
  KEY `no_index` (`purchase_no`,`sku`,`production_date`) USING BTREE,
  KEY `idx_origin_id_purchase_no` (`origin_id`,`purchase_no`),
  KEY `idx_sku` (`sku`,`purchase_no`),
  KEY `idx_arrival_date` (`latest_arrival_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='采购单详单表，通过purchase_no和采购单主表关联';

-- xianmudb.stock_arrange definition

CREATE TABLE `stock_arrange` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `purchase_no` varchar(30) DEFAULT NULL COMMENT '采购单编号(purchases表purchase_no字段)',
  `stock_task_id` int(11) DEFAULT NULL COMMENT '任务id（stock_task表id字段）',
  `state` tinyint(4) DEFAULT NULL COMMENT '状态:0：正常,1：已完成,2：已取消，3：已关闭',
  `arrange_time` date DEFAULT NULL COMMENT '预约入库时间',
  `admin_id` int(11) DEFAULT NULL COMMENT '发起人id',
  `admin_name` varchar(30) DEFAULT NULL COMMENT '发起人',
  `arrange_remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
  `is_close` tinyint(4) DEFAULT '0' COMMENT '操作标识:0未更新1已更新 （乐观锁）',
  `multi_supplier` tinyint(4) DEFAULT '0' COMMENT '是否是多供应商0否1是',
  `supplier_id` bigint(20) DEFAULT NULL COMMENT '供应商id（supplier表中ID字段）',
  `source` varchar(255) NOT NULL DEFAULT 'xianmu' COMMENT '来源:xianmu,saas',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户id',
  `warehouse_no` int(11) DEFAULT NULL COMMENT '仓库编号(warehouse_storage_center表中的warehouse_no字段)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_stock_task_id` (`stock_task_id`) USING BTREE,
  KEY `idx_purchase_no` (`purchase_no`) USING BTREE,
  KEY `idx_arrange_time` (`arrange_time`,`warehouse_no`,`state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='入库预约单表,通过purchase_no和采购单表关联';

-- xianmudb.stock_arrange_item definition

CREATE TABLE `stock_arrange_item` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `stock_arrange_id` int(11) DEFAULT NULL COMMENT '预约单id',
  `sku` varchar(30) DEFAULT NULL COMMENT 'sku(关联inventory表的sku字段)',
  `arrival_quantity` int(11) DEFAULT '0' COMMENT '预约数量',
  `actual_quantity` int(11) DEFAULT '0' COMMENT '实到数量',
  `type` tinyint(4) DEFAULT NULL COMMENT '商品归属: 0 自营 1 代仓',
  `quality_time` int(11) DEFAULT NULL COMMENT '保质期时长',
  `quality_time_unit` varchar(10) DEFAULT NULL COMMENT '保质期时长单位',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
  `supplier_id` bigint(20) DEFAULT NULL COMMENT '供应商id(supplier表中ID字段)',
  `abnormal_quantity` int(11) DEFAULT '0' COMMENT '异常数量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_sku` (`sku`) USING BTREE,
  KEY `idx_supplierid_stockarrangeid` (`supplier_id`,`stock_arrange_id`),
  KEY `idx_stock_arrange_id_sku` (`stock_arrange_id`,`sku`,`arrival_quantity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='入库预约单品维度详单，通过stock_arrange_id和入库预约单表关联';

-- xianmudb.stock_arrange_item_detail definition

CREATE TABLE `stock_arrange_item_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `sku` varchar(30) DEFAULT NULL COMMENT 'sku(关联inventory表的sku字段)',
  `stock_arrange_item_id` int(11) DEFAULT NULL COMMENT '预约单条目id(stock_arrange_item表id字段)',
  `stock_arrange_id` int(11) DEFAULT NULL COMMENT '预约单id(stock_arrange表id字段)',
  `stock_task_id` int(11) DEFAULT NULL COMMENT '任务id',
  `arr_quantity` int(10) unsigned DEFAULT '0' COMMENT '预约数量',
  `quantity` int(11) DEFAULT '0' COMMENT '到货数量',
  `production_date` date DEFAULT NULL COMMENT '生产日期',
  `quality_date` date DEFAULT NULL COMMENT '保质期',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `creator` varchar(255) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
  `updater` varchar(255) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_stock_task_id` (`stock_task_id`) USING BTREE,
  KEY `idx_sku` (`sku`) USING BTREE,
  KEY `idx_item_id` (`stock_arrange_item_id`) USING BTREE,
  KEY `idx_stockarrangeid_sku` (`stock_arrange_id`,`sku`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='入库预约单品+效期维度详单，通过stock_arrange_id关联入口预约单表，通过stock_arrange_item_id关联入库预约单品维度详单表';

-- xianmudb.pms_order_trace_record definition

CREATE TABLE `pms_order_trace_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
  `order_no` varchar(64) DEFAULT NULL COMMENT '关联单号：type=11,25：采购预约单号;type=56:采退单号',
  `extend_no` varchar(64) DEFAULT NULL COMMENT '拓展单号：type=11,25,56：采购单号',
  `sku` varchar(64) DEFAULT NULL COMMENT 'sku',
  `operate_no` varchar(64) DEFAULT NULL COMMENT '操作单号，wms回告单号',
  `type` int(11) DEFAULT NULL COMMENT '跟踪类型：11.采购入库;25:越库入库;56:采购退货出库',
  `quantity` int(11) DEFAULT NULL COMMENT '数量',
  `operate_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operator_name` varchar(64) DEFAULT NULL COMMENT '操作人',
  `current_no` varchar(64) DEFAULT NULL COMMENT '当前记录相关编号：type=11,25:采入仓;type=56:出库仓;',
  `relation_no` varchar(64) DEFAULT NULL COMMENT '当前记录间接关联编号：type=11,25,56:供应商ID;',
  `produce_at` date DEFAULT NULL COMMENT '生产日期',
  `shelf_life` date DEFAULT NULL COMMENT '保质期',
  `weight` decimal(10,2) DEFAULT NULL COMMENT '重量(毛重)',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `tenant_id` bigint(20) unsigned DEFAULT NULL COMMENT '租户ID，跟随业务单据(采购单，预约单，采退单)，不随操作单',
  PRIMARY KEY (`id`),
  KEY `order_no_idx` (`order_no`),
  KEY `extend_no_type_idx` (`extend_no`,`type`),
  KEY `extend_no_sku_type_idx` (`extend_no`,`sku`,`type`),
  KEY `op_no_idx` (`operate_no`)
) ENGINE=InnoDB AUTO_INCREMENT=4378 DEFAULT CHARSET=utf8mb4 COMMENT='订单跟踪记录(采购单，采购退货单履约执行记录,仓库操作维度)';

-- xianmudb.supplier definition

CREATE TABLE `supplier` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `name` varchar(50) DEFAULT NULL COMMENT '供应商名称',
  `category_array` varchar(1000) DEFAULT NULL COMMENT '经营范围',
  `product_array` varchar(500) DEFAULT NULL COMMENT '供应品类',
  `manager` varchar(50) DEFAULT NULL COMMENT '供应商管理人',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `invoice` tinyint(1) DEFAULT '1' COMMENT '是否开发票',
  `supplier_type` tinyint(4) DEFAULT '0' COMMENT '0企业（生产商），1个人，2企业（经销商）',
  `tax_number` varchar(20) DEFAULT NULL COMMENT '供应商工商信息（税号字段/身份证号）',
  `type` tinyint(4) DEFAULT '0' COMMENT '企业（生产商），1个人，2企业（经销商）【无用字段】',
  `status` tinyint(4) DEFAULT '0' COMMENT '0启用，1停用,2审核中，3已关闭',
  `audit_pass_date` date DEFAULT NULL COMMENT '审核通过日期（用于计算剩余天数）',
  `creator` varchar(50) DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(45) DEFAULT NULL COMMENT '最后修改人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `qr_code_show_supplier_switch` tinyint(4) NOT NULL DEFAULT '0' COMMENT '二维码是否展示供应商信息 0不展示1展示',
  `source` varchar(255) NOT NULL DEFAULT 'xianmu' COMMENT '来源:xianmu,saas',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户id',
  `business_type` tinyint(4) DEFAULT NULL COMMENT '业务类型，1代销',
  `customer_supplier_id` varchar(64) DEFAULT NULL COMMENT '外部供应商id'
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_name` (`name`) USING BTREE,
  KEY `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='供应商表';