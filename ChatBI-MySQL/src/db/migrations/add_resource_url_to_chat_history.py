#!/usr/bin/env python3
"""
数据库迁移脚本：为chat_history表添加resource_url字段
"""

import sys
import os
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
chatbi_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, chatbi_root)

from src.db.connection import get_db_connection
from src.utils.logger import logger


def add_resource_url_column():
    """
    为chat_history表添加resource_url字段
    """
    conn = None
    cursor = None
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'chat_history' 
            AND COLUMN_NAME = 'resource_url'
        """)
        
        result = cursor.fetchone()
        if result[0] > 0:
            logger.info("resource_url字段已存在，跳过添加")
            return True
        
        # 添加resource_url字段
        logger.info("开始为chat_history表添加resource_url字段...")
        
        cursor.execute("""
            ALTER TABLE chat_history 
            ADD COLUMN resource_url TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci 
            COMMENT '资源URL，支持多个URL用逗号分隔，如图片、文档等'
        """)
        
        # 添加索引以提高查询性能
        cursor.execute("""
            ALTER TABLE chat_history 
            ADD INDEX idx_resource_url (resource_url(255))
        """)
        
        conn.commit()
        logger.info("resource_url字段添加成功")
        return True
        
    except Exception as e:
        logger.error(f"添加resource_url字段失败: {e}", exc_info=True)
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def migrate_existing_data():
    """
    迁移现有的JSON格式数据到新的字段结构
    """
    conn = None
    cursor = None
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        logger.info("开始迁移现有的JSON格式数据...")
        
        # 查找包含JSON格式的用户消息
        cursor.execute("""
            SELECT id, content 
            FROM chat_history 
            WHERE role = 'user' 
            AND content LIKE '%"images"%'
            AND resource_url IS NULL
        """)
        
        messages = cursor.fetchall()
        logger.info(f"找到 {len(messages)} 条需要迁移的消息")
        
        migrated_count = 0
        
        for message in messages:
            try:
                # 尝试解析JSON
                content_data = json.loads(message['content'])
                
                if isinstance(content_data, dict) and 'text' in content_data and 'images' in content_data:
                    text = content_data['text']
                    images = content_data['images']
                    
                    if images and isinstance(images, list):
                        # 将图片URL列表转换为逗号分隔的字符串
                        resource_url = ','.join(images)
                        
                        # 更新记录
                        update_cursor = conn.cursor()
                        update_cursor.execute("""
                            UPDATE chat_history 
                            SET content = %s, resource_url = %s 
                            WHERE id = %s
                        """, (text, resource_url, message['id']))
                        update_cursor.close()
                        
                        migrated_count += 1
                        logger.debug(f"迁移消息 ID {message['id']}: 文本='{text[:50]}...', 图片数量={len(images)}")
                        
            except json.JSONDecodeError:
                # 不是JSON格式，跳过
                continue
            except Exception as e:
                logger.warning(f"迁移消息 ID {message['id']} 时出错: {e}")
                continue
        
        conn.commit()
        logger.info(f"数据迁移完成，成功迁移 {migrated_count} 条记录")
        return True
        
    except Exception as e:
        logger.error(f"数据迁移失败: {e}", exc_info=True)
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def verify_migration():
    """
    验证迁移结果
    """
    conn = None
    cursor = None
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 检查字段是否存在
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'chat_history' 
            AND COLUMN_NAME = 'resource_url'
        """)
        
        if cursor.fetchone()['count'] == 0:
            logger.error("resource_url字段不存在")
            return False
        
        # 检查迁移的数据
        cursor.execute("""
            SELECT COUNT(*) as total_messages,
                   COUNT(CASE WHEN resource_url IS NOT NULL AND resource_url != '' THEN 1 END) as messages_with_resources
            FROM chat_history 
            WHERE role = 'user'
        """)
        
        result = cursor.fetchone()
        logger.info(f"验证结果: 总用户消息数={result['total_messages']}, 包含资源的消息数={result['messages_with_resources']}")
        
        # 检查是否还有JSON格式的消息
        cursor.execute("""
            SELECT COUNT(*) as json_messages
            FROM chat_history 
            WHERE role = 'user' 
            AND content LIKE '%"images"%'
            AND resource_url IS NULL
        """)
        
        json_count = cursor.fetchone()['json_messages']
        if json_count > 0:
            logger.warning(f"仍有 {json_count} 条JSON格式的消息未迁移")
        else:
            logger.info("所有JSON格式的消息已成功迁移")
        
        return True
        
    except Exception as e:
        logger.error(f"验证迁移结果失败: {e}", exc_info=True)
        return False
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def main():
    """
    主迁移函数
    """
    logger.info("开始chat_history表resource_url字段迁移")
    
    # 步骤1: 添加字段
    if not add_resource_url_column():
        logger.error("添加字段失败，终止迁移")
        return False
    
    # 步骤2: 迁移数据
    if not migrate_existing_data():
        logger.error("数据迁移失败，但字段已添加")
        return False
    
    # 步骤3: 验证结果
    if not verify_migration():
        logger.error("迁移验证失败")
        return False
    
    logger.info("chat_history表resource_url字段迁移完成")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
