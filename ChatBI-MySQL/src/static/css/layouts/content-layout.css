/**
 * 内容布局样式
 *
 * 定义内容区域的布局样式，适用于聊天界面和其他内容页面
 * 遵循 Apple/OpenAI 设计风格
 */

/* 内容容器 */
.content-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 4rem);
    min-width: 0;
    overflow: hidden;
}

/* 滚动区域 */
.scroll-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    /* 确保平滑过渡 */
    transition: height var(--duration-normal) ease;
    /* 确保Safari滚动流畅 */
    -webkit-overflow-scrolling: touch;
}

/* 内容区域 */
.content-area {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 100%;
    padding-bottom: 1rem; /* 确保底部有足够空间 */
}

/* 内容容器 - 确保内容居中且宽度适中 */
.content-container-centered {
    max-width: 48rem;
    margin: 0 auto;
    width: 100%;
}

/* 底部区域 */
.bottom-area {
    flex-shrink: 0;
    position: relative;
    z-index: 10; /* 确保在内容区域之上 */
}

/* 响应式调整 */
@media (max-width: 768px) {
    .content-container {
        height: calc(100vh - 3.5rem); /* 移动端头部可能更小 */
    }
}

/* 聊天特定样式 - 保留原有类名以避免大量修改 */
.chat-content-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 4rem);
    min-width: 0;
    overflow: hidden;
    position: relative; /* 为WelcomeTitle提供定位基准 */
}

.chat-scroll-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    transition: height var(--duration-normal) ease;
    -webkit-overflow-scrolling: touch;
}

.chat-messages-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 100%;
    padding-bottom: 1rem;
}

.chat-message-container {
    max-width: 48rem;
    margin: 0 auto;
    width: 100%;
}

.chat-bottom-area {
    flex-shrink: 0;
    position: relative;
    z-index: 10;
}

.chat-input-area {
    background-color: var(--color-bg-primary);
    backdrop-filter: blur(8px);
    position: relative;
    z-index: 11;
}

[data-theme="dark"] .chat-input-area {
    background-color: var(--color-bg-primary);
}

.chat-log-panel-container {
    overflow: hidden;
    transition: max-height var(--duration-normal) ease-in-out;
}

@media (max-width: 768px) {
    .chat-content-container {
        height: calc(100vh - 3.5rem);
    }
}
