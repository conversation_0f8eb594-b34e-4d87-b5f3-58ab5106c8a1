/**
 * Question Prompts Component
 *
 * 显示预设问题提示的组件，结合了欢迎模块和模板按钮的功能
 * 遵循项目的Apple/OpenAI设计风格，提供优雅的用户体验
 */

import { computed } from 'vue';

export default {
    name: 'QuestionPrompts',
    props: {
        show: {
            type: Boolean,
            default: true
        },
        isHistoryTransition: {
            type: Boolean,
            default: false
        }
    },
    emits: ['question-click'],
    setup(props, { emit }) {
        // 预设问题列表
        const questions = [
            {
                id: 1,
                text: '我的客户中，近1个月购买日清山茶花高筋粉的客户有哪些？'
            },
            {
                id: 2,
                text: '我的客户中，过去3个月购买了菲诺厚椰乳的有哪些，列出它们的手机号、名字、总下单金额、最后下单日'
            },
            {
                id: 3,
                text: '我的客户中，近3个月内购买过全品类商品（剔除水果）的有哪些，拉出购买的产品名字，客户名字、手机号'
            },
            {
                id: 4,
                text: '日清山茶花高筋粉 在深圳的到货时间？'
            },
            {
                id: 5,
                text: '日清山茶花高筋粉 最新的质检报告？'
            },
            {
                id: 6,
                text: '我的客户中，近半年购买过大于等于2个pb品类且自然月pb单品类金额满足200的客户有哪些？并且告诉我这些客户购买的pb产品是什么，不用罗列每笔订单金额'
            },
            {
                id: 7,
                text: '拉新统计：我的客户中，哪些是今天才首次下单的？列出它们的名字、手机号、下单金额、下单时间'
            },
            {
                id: 8,
                text: '有效拜访统计：我的团队的今日有效拜访数量，按照BD分组展示'
            }
        ];

        // 处理问题点击
        const handleQuestionClick = (question) => {
            emit('question-click', question);
        };

        // 根据上下文选择过渡动画名称
        const transitionName = computed(() => {
            return props.isHistoryTransition ? 'question-list-instant' : 'question-list';
        });

        return {
            questions,
            handleQuestionClick,
            transitionName
        };
    },
    template: `
        <!-- 问题列表区域 - 带过渡动画和延迟 -->
        <transition :name="transitionName" appear>
            <div v-if="show" class="question-prompts-container">
                <h1 class="question-prompts-main-title">欢迎使用ChatBI</h1>
                <p class="question-prompts-subtitle">您可以尝试询问以下问题，或直接输入您的问题</p>
                <div class="question-prompts-list scrollbar-auto">
                    <button
                        v-for="(question, index) in questions"
                        :key="question.id"
                        @click="handleQuestionClick(question)"
                        class="question-prompt-item"
                        :style="{ animationDelay: (index * 60 + 400) + 'ms' }"
                        :title="question.text"
                    >
                        <span class="question-prompt-text">{{ question.text }}</span>
                    </button>
                </div>
            </div>
        </transition>
    `
};
