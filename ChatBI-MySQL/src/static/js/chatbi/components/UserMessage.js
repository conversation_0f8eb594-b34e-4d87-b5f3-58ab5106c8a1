import { ref } from 'vue';
import { CopyIcon, CheckIcon } from '../../utils/Icons.js';

export default {
    name: 'UserMessage',
    props: {
        id: {
            type: String,
            required: true
        },
        content: {
            type: String,
            required: true
        },
        images: {
            type: Array,
            default: () => []
        },
        timestamp: {
            type: String,
            default: ''
        }
    },
    setup() {
        const copied = ref(false);

        // 复制消息内容
        const copyContent = (text) => {
            navigator.clipboard.writeText(text)
                .then(() => {
                    copied.value = true;
                    setTimeout(() => {
                        copied.value = false;
                    }, 2000);
                })
                .catch(err => {
                    console.error('复制失败:', err);
                });
        };

        // 打开图片模态框
        const openImageModal = (imageUrl) => {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 cursor-pointer';
            modal.onclick = () => document.body.removeChild(modal);

            const img = document.createElement('img');
            img.src = imageUrl;
            img.className = 'max-w-[90vw] max-h-[90vh] object-contain';
            img.onclick = (e) => e.stopPropagation();

            modal.appendChild(img);
            document.body.appendChild(modal);
        };

        return {
            copyContent,
            copied,
            openImageModal,
            CopyIcon,
            CheckIcon
        };
    },
    template: `
        <div :id="'message-' + id" class="flex flex-col items-end mb-6 group min-w-0 message user-message px-4" :data-message-id="id">
            <div class="card relative w-auto max-w-[75%] ml-auto rounded-2xl min-w-0 user-message-bubble">
                <div class="card-body px-3 py-2 min-w-0 user-message-content">
                    <!-- 图片显示区域 -->
                    <div v-if="images && images.length > 0" class="mb-3">
                        <div class="grid gap-2" :class="images.length === 1 ? 'grid-cols-1' : 'grid-cols-2'">
                            <div v-for="(image, index) in images" :key="index" class="relative">
                                <img
                                    :src="image"
                                    :alt="'用户上传的图片 ' + (index + 1)"
                                    class="w-full h-auto max-w-xs rounded-lg border border-base-300 cursor-pointer hover:opacity-80 transition-opacity"
                                    @click="openImageModal(image)"
                                    loading="lazy"
                                />
                            </div>
                        </div>
                    </div>
                    <!-- 文本内容 -->
                    <p v-if="content.trim()" class="whitespace-pre-wrap break-words">{{ content }}</p>
                </div>
            </div>

            <div class="flex items-center justify-end gap-1 mt-1 opacity-0 group-hover:opacity-100 transition-all duration-300 message-footer user-message-footer">
                <span class="text-xs opacity-70 message-timestamp">{{ timestamp }}</span>
                <button
                    class="btn btn-square btn-xs btn-ghost message-action-button"
                    @click="copyContent(content)"
                    title="复制"
                >
                    <span v-if="!copied" class="message-action-icon" v-html="CopyIcon"></span>
                    <span v-else class="message-action-icon" v-html="CheckIcon"></span>
                </button>
            </div>
        </div>
    `
};
