/**
 * Message Formatter Composable
 *
 * Handles formatting and standardization of chat messages
 */

/**
 * 生成唯一消息ID
 * @param {string} prefix 前缀
 * @returns {string} 唯一ID
 */
const generateMessageId = (prefix = 'msg') => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

/**
 * 解析用户消息内容，提取文本和图片
 * @param {string} content 消息内容
 * @returns {Object} 包含text和images的对象
 */
const parseUserMessageContent = (content) => {
    try {
        // 尝试解析JSON格式的消息内容
        const parsed = JSON.parse(content);
        if (parsed && typeof parsed === 'object' && 'text' in parsed) {
            return {
                text: parsed.text || '',
                images: parsed.images || []
            };
        }
    } catch (e) {
        // 如果不是JSON格式，则视为纯文本
    }

    // 返回纯文本格式
    return {
        text: content,
        images: []
    };
};

/**
 * 格式化消息
 * @param {Object} msg 原始消息对象
 * @returns {Object} 格式化后的消息对象
 */
const formatMessage = (msg) => {
    // 如果日志是字符串类型，保持原样；如果是数组，保持原样；否则设为空数组
    let formattedLogs = msg.logs;
    if (!msg.logs) {
        formattedLogs = [];
    }

    // 解析用户消息内容（如果是用户消息且包含图片信息）
    let content = msg.content;
    let images = [];

    if (msg.role === 'user') {
        const parsed = parseUserMessageContent(msg.content);
        content = parsed.text;
        images = parsed.images;
    }

    return {
        id: msg.id ? String(msg.id) : generateMessageId(), // 确保ID是字符串类型
        role: msg.role,
        content: content,
        images: images, // 添加图片数组
        logs: formattedLogs, // 保留日志数据，保持原始类型
        timestamp: msg.timestamp ? new Date(parseInt(msg.timestamp)).toLocaleTimeString() : new Date().toLocaleTimeString(),
        isBadCase: msg.isBadCase || false, // 添加 bad case 状态
        isGoodCase: msg.isGoodCase || false // 添加 good case 状态
    };
};

/**
 * 创建用户消息对象
 * @param {Object} userMessage 用户消息数据
 * @returns {Object} 格式化的用户消息对象
 */
const createUserMessage = (userMessage) => {
    const messageId = generateMessageId('user');
    return {
        id: messageId,
        role: 'user',
        content: userMessage.content,
        images: userMessage.images || [], // 添加图片支持
        timestamp: new Date(userMessage.timestamp).toLocaleTimeString()
    };
};

/**
 * 创建AI消息对象（初始空消息，用于流式更新）
 * @returns {Object} 初始AI消息对象
 */
const createAiMessage = () => {
    const newMessageId = generateMessageId('ai');
    return {
        id: newMessageId,
        role: 'assistant',
        content: '',
        renderedContent: '',
        timestamp: new Date().toLocaleTimeString(),
        isStreaming: true,
        isError: false,
        isInterrupted: false // 添加中断状态标记
    };
};

/**
 * 创建历史记录用的AI消息对象
 * @param {Object} message 消息对象
 * @param {Object} messageData 消息数据
 * @returns {Object} 格式化的AI消息对象
 */
const createHistoryAiMessage = (message, messageData) => {
    return {
        id: message.id,
        role: 'assistant',
        content: messageData.content,
        timestamp: messageData.timestamp,
        logs: messageData.logs || message.logs || [] // 保存日志数据
    };
};

/**
 * 使用消息格式化器
 * @returns {Object} 消息格式化方法
 */
export function useMessageFormatter() {
    return {
        formatMessage,
        createUserMessage,
        createAiMessage,
        createHistoryAiMessage,
        generateMessageId
    };
}
