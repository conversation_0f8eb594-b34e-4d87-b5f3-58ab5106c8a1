/**
 * ConversationTableCard Component
 *
 * A modern, elegant table for displaying conversations
 * Uses DaisyUI table component with responsive design
 * Follows Apple/OpenAI-inspired aesthetic
 */
import { computed, inject } from 'vue';
import BaseDashboardCard from './BaseDashboardCard.js';
import {
    ExclamationTriangleIcon,
    ChevronLeftIcon,
    ChevronRightIcon,
    EyeIcon
} from '../../../utils/Icons.js';

export default {
    name: 'ConversationTableCard',
    components: {
        BaseDashboardCard
    },
    props: {
        conversations: {
            type: Array,
            required: true
        },
        isLoading: {
            type: Boolean,
            default: false
        },
        totalCount: {
            type: Number,
            default: 0
        },
        currentPage: {
            type: Number,
            default: 1
        },
        pageSize: {
            type: Number,
            default: 20
        },
        pageSizeOptions: {
            type: Array,
            default: () => [
                { value: 10, label: '10条/页' },
                { value: 20, label: '20条/页' },
                { value: 50, label: '50条/页' },
                { value: 100, label: '100条/页' }
            ]
        }
    },
    emits: ['page-change', 'page-size-change'],
    setup(props, { emit }) {
        // 注入全局模态框控制
        const conversationModal = inject('conversationModal');

        // Open conversation detail modal
        const openConversationDetail = (conversation) => {
            // In a real implementation, we would fetch the full conversation data
            // For now, we'll just use the conversation object passed in
            console.log('ConversationTableCard: openConversationDetail called with', conversation);
            conversationModal.openConversationDetail(conversation);
        };

        // Handle pagination
        const goToPage = (page) => {
            if (page !== props.currentPage) {
                emit('page-change', page);
            }
        };

        // Handle page size change
        const handlePageSizeChange = (newPageSize) => {
            emit('page-size-change', parseInt(newPageSize));
        };

        // Get repair status display info
        const getRepairStatusInfo = (repairStatus) => {
            switch (repairStatus) {
                case 0:
                    return { text: '未修复', class: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' };
                case 1:
                    return { text: '已修复', class: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' };
                case 2:
                    return { text: '暂不修复', class: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' };
                default:
                    return null;
            }
        };

        // Get agent display info with fully dynamic styling
        const getAgentDisplayInfo = (agentName) => {
            // 完全动态的颜色生成 - 基于HSL色彩空间
            const generateDynamicColor = (str) => {
                // 生成稳定的哈希值
                let hash = 0;
                for (let i = 0; i < str.length; i++) {
                    const char = str.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash;
                }
                hash = Math.abs(hash);

                // 使用黄金比例确保颜色分布均匀且美观
                const goldenRatio = 0.618033988749;
                const hue = (hash * goldenRatio) % 1 * 360;

                // 固定饱和度和亮度确保颜色和谐且易读
                const saturation = 65; // 适中的饱和度
                const lightness = 50;  // 适中的亮度

                return {
                    hue: Math.round(hue),
                    saturation,
                    lightness
                };
            };

            const color = generateDynamicColor(agentName);

            // 动态生成CSS自定义属性
            const dynamicStyle = {
                '--agent-hue': color.hue,
                '--agent-saturation': `${color.saturation}%`,
                '--agent-lightness': `${color.lightness}%`
            };

            return {
                displayName: agentName, // 直接显示后端返回的名称
                dynamicStyle: dynamicStyle,
                originalName: agentName
            };
        };

        // Computed properties for pagination
        const showingFrom = computed(() => {
            return Math.min((props.currentPage - 1) * props.pageSize + 1, props.totalCount);
        });

        const showingTo = computed(() => {
            return Math.min(props.currentPage * props.pageSize, props.totalCount);
        });

        const canGoPrevious = computed(() => {
            return props.currentPage > 1;
        });

        const canGoNext = computed(() => {
            return props.currentPage * props.pageSize < props.totalCount;
        });

        return {
            openConversationDetail,
            goToPage,
            handlePageSizeChange,
            getRepairStatusInfo,
            getAgentDisplayInfo,
            showingFrom,
            showingTo,
            canGoPrevious,
            canGoNext,
            // Icons
            ExclamationTriangleIcon,
            ChevronLeftIcon,
            ChevronRightIcon,
            EyeIcon
        };
    },
    template: `
        <BaseDashboardCard size="auto" :loading="isLoading" card-class="min-h-[500px]">
            <div class="conversation-list-container">
                <!-- Empty State -->
                <div v-if="!isLoading && conversations.length === 0" class="flex flex-col items-center justify-center py-12">
                    <div class="text-4xl mb-4">🔍</div>
                    <p class="text-base-content/70 text-sm">没有找到符合条件的会话</p>
                </div>

                <!-- Conversations Table -->
                <div v-else-if="!isLoading" class="overflow-x-auto">
                    <table class="table rounded-xl overflow-hidden">
                        <thead>
                            <tr>
                                <th>用户</th>
                                <th>标题</th>
                                <th class="hidden md:table-cell">助手</th>
                                <th class="hidden md:table-cell">时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="conversation in conversations" :key="conversation.id"
                                @click="openConversationDetail(conversation)"
                                class="hover:bg-base-200/50 cursor-pointer">
                                <td>
                                    <div class="flex flex-col">
                                        <div class="font-medium">{{ conversation.username }}</div>
                                        <div class="text-xs opacity-70">{{ conversation.email || '<EMAIL>' }}</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center gap-2 flex-wrap">
                                        <span class="truncate max-w-xs">{{ conversation.title }}</span>
                                        <div class="flex items-center gap-1">
                                            <span v-if="conversation.isGoodCase" class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium" :class="{'bg-green-100 text-green-800': true, 'dark-mode-badge': true}">
                                                Good Case
                                            </span>
                                            <span v-if="conversation.isBadCase" class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium" :class="{'bg-red-100 text-red-800': true, 'dark-mode-badge': true}">
                                                Bad Case
                                            </span>
                                            <span v-if="conversation.isBadCase && conversation.repairStatus !== undefined && getRepairStatusInfo(conversation.repairStatus)"
                                                  class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium"
                                                  :class="getRepairStatusInfo(conversation.repairStatus).class">
                                                {{ getRepairStatusInfo(conversation.repairStatus).text }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="hidden md:table-cell">
                                    <div class="flex flex-wrap gap-2" v-if="conversation.agents && conversation.agents.length > 0">
                                        <div v-for="agent in conversation.agents"
                                             :key="agent"
                                             class="agent-tag agent-tag-dynamic"
                                             :style="getAgentDisplayInfo(agent).dynamicStyle">
                                            <span class="agent-name">{{ getAgentDisplayInfo(agent).displayName }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="hidden md:table-cell text-base-content/70">{{ conversation.lastMessageTime }}</td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 py-4">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                            <div class="text-sm text-base-content/70">
                                显示 {{ showingFrom }} - {{ showingTo }} 条，共 {{ totalCount }} 条
                            </div>
                            <!-- 分页大小选择器 -->
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-base-content/70">每页显示:</span>
                                <select
                                    class="select select-sm select-bordered w-24 h-8 min-h-8 text-sm dark:border-base-content/20 dark:border"
                                    :value="pageSize"
                                    @change="handlePageSizeChange($event.target.value)"
                                >
                                    <option
                                        v-for="option in pageSizeOptions"
                                        :key="option.value"
                                        :value="option.value"
                                    >
                                        {{ option.value }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="join">
                            <button
                                class="join-item btn btn-sm"
                                :disabled="!canGoPrevious"
                                @click="goToPage(currentPage - 1)"
                            >
                                <span v-html="ChevronLeftIcon"></span>
                            </button>
                            <button class="join-item btn btn-sm btn-active">
                                {{ currentPage }}
                            </button>
                            <button
                                class="join-item btn btn-sm"
                                :disabled="!canGoNext"
                                @click="goToPage(currentPage + 1)"
                            >
                                <span v-html="ChevronRightIcon"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </BaseDashboardCard>
    `
};
