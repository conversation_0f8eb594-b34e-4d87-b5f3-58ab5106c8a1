"""
Chat quality service module.

This module provides business logic for managing chatbot quality and bad cases.
"""

import functools
from typing import Optional

from src.utils.logger import logger
from src.repositories.chatbi.bad_case import (
    mark_conversation_as_bad_case,
    get_conversation_bad_case_status,
    update_bad_case_repair_status,
)


def feishu_notification_decorator(func):
    """
    装饰器：在mark_bad_case执行成功后发送飞书机器人消息到群聊

    Args:
        func: 被装饰的函数

    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 执行原始函数
        result = func(*args, **kwargs)

        # 如果执行成功，则发送通知
        if not result:
            return result

        # 获取参数值
        conversation_id = kwargs.get('conversation_id', args[0] if args else None)
        is_bad_case = kwargs.get('is_bad_case', args[1] if len(args) > 1 else True)
        user_name = kwargs.get('user_name', args[2] if len(args) > 2 else None)

        # 获取最后一条用户消息
        last_user_message = _get_last_user_message(conversation_id)

        # 发送通知
        _send_notification(conversation_id, is_bad_case, user_name, last_user_message)

        return result

    def _get_last_user_message(conversation_id: str) -> Optional[str]:
        """获取对话中最后一条用户消息"""
        try:
            from src.repositories.chatbi.history import load_conversation
            messages = load_conversation(conversation_id)
            
            if not messages:
                logger.warning(f"对话 {conversation_id} 没有消息历史")
                return None

            # 从后往前查找最后一条用户消息
            for message in reversed(messages):
                if message.get('role') == 'user':
                    content = message.get('content', '').strip()
                    if content:
                        logger.info(f"获取到最后一个用户消息内容，长度: {len(content)}")
                        return content
                    break

            logger.warning(f"对话 {conversation_id} 中未找到用户消息")
            return None

        except Exception as e:
            logger.error(f"获取对话 {conversation_id} 最后用户消息时发生异常: {str(e)}", exc_info=True)
            return None

    def _send_notification(conversation_id: str, is_bad_case: bool, user_name: Optional[str], last_user_message: Optional[str]) -> None:
        """发送通知到飞书群聊"""
        try:
            from src.services.feishu.message_apis import send_bad_case_notification, send_bad_case_unmark_notification
            
            notification_func = send_bad_case_notification if is_bad_case else send_bad_case_unmark_notification
            action = "标记" if is_bad_case else "取消标记"

            notification_sent = notification_func(conversation_id, user_name, last_user_message)
            
            if notification_sent:
                logger.info(f"已发送Bad Case{action}通知到群聊，对话ID: {conversation_id}")
            else:
                logger.warning(f"发送Bad Case{action}通知失败，对话ID: {conversation_id}")

        except Exception as e:
            action = "标记" if is_bad_case else "取消标记"
            logger.error(f"发送Bad Case{action}通知时发生异常: {str(e)}", exc_info=True)

    return wrapper

@feishu_notification_decorator
def mark_bad_case(conversation_id: str, is_bad_case: bool = True, user_name: str = None) -> bool:
    """
    Mark or unmark a conversation as a bad case.

    Args:
        conversation_id (str): The ID of the conversation
        is_bad_case (bool, optional): Whether to mark as bad case (True) or unmark (False). Defaults to True.
        user_name (str, optional): The name of the user who marked the bad case. Used for notification. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    action = "marked" if is_bad_case else "unmarked"
    logger.info(f"User {action} conversation {conversation_id} as bad case, user_name: {user_name}")
    return mark_conversation_as_bad_case(conversation_id, is_bad_case, marked_by=user_name)

def is_bad_case(conversation_id: str, username: Optional[str] = None, email: Optional[str] = None) -> bool:
    """
    Check if a conversation is marked as a bad case.

    Args:
        conversation_id (str): The ID of the conversation
        username (str, optional): The username to filter by. Defaults to None (kept for API compatibility).
        email (str, optional): The email to filter by. Defaults to None (kept for API compatibility).

    Returns:
        bool: True if the conversation is marked as a bad case, False otherwise
    """
    logger.info(f"Checking if conversation {conversation_id} is a bad case")
    # Note: username and email parameters are kept for API compatibility but not used
    # since the new bad_case table design doesn't require user filtering
    return get_conversation_bad_case_status(conversation_id)





def update_repair_status(conversation_id: str, repair_status: int, username: Optional[str] = None, email: Optional[str] = None) -> bool:
    """
    Update the repair status of a bad case.

    Args:
        conversation_id (str): The ID of the conversation
        repair_status (int): The new repair status (0=未修复, 1=已修复, 2=暂不修复)
        username (str, optional): The username to filter by. Defaults to None (kept for API compatibility).
        email (str, optional): The email to filter by. Defaults to None (kept for API compatibility).

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    logger.info(f"Updating repair status for conversation {conversation_id} to {repair_status}")
    # Note: username and email parameters are kept for API compatibility but not used
    return update_bad_case_repair_status(conversation_id, repair_status)
