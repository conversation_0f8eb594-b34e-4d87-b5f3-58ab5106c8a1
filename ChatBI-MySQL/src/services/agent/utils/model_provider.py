"""
Agent的模型提供者配置
"""
import os
from openai import AsyncOpenAI
from src.utils.logger import logger
from agents import ModelProvider, Model, OpenAIChatCompletionsModel, set_tracing_disabled
from agents.extensions.models.litellm_model import LitellmModel

# 基础模型配置
OPENAI_API_KEY = os.getenv("XM_OPENAI_API_KEY")
OPENAI_API_BASE = os.getenv("OPENAI_API_BASE")
OPENAI_MODEL = os.getenv("OPENAI_MODEL")
OPENAI_MODEL_SETTINGS = os.getenv("OPENAI_MODEL_SETTINGS")

# 快模型(LITE)配置
LITE_OPENAI_API_KEY = os.getenv("LITE_XM_OPENAI_API_KEY", OPENAI_API_KEY)
LITE_OPENAI_API_BASE = os.getenv("LITE_OPENAI_API_BASE", OPENAI_API_BASE)
LITE_OPENAI_MODEL = os.getenv("LITE_OPENAI_MODEL", OPENAI_MODEL)
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")

# 初始化 OpenAI 客户端
default_client = AsyncOpenAI(base_url=OPENAI_API_BASE, api_key=OPENAI_API_KEY)
lite_client = AsyncOpenAI(base_url=LITE_OPENAI_API_BASE, api_key=LITE_OPENAI_API_KEY)
set_tracing_disabled(disabled=True)

# Litellm 模型配置 (模型名称需 "openai/" 前缀)
LITE_LLM_FAST_MODEL = LitellmModel(model=f"openai/{LITE_OPENAI_MODEL}", api_key=LITE_OPENAI_API_KEY, base_url=LITE_OPENAI_API_BASE)
LITE_LLM_MODEL = LitellmModel(model=f"openai/{OPENAI_MODEL}", api_key=OPENAI_API_KEY, base_url=OPENAI_API_BASE)


class CustomModelProvider(ModelProvider):
    """为 OpenAI 模型自定义的模型提供者"""

    def __init__(self, model_name: str = None, client: AsyncOpenAI = default_client):
        super().__init__()
        self.model_name = model_name
        self.client = client
    
    def get_model(self, model_name: str = None) -> Model:
        """
        获取 Agent 要使用的模型。

        Args:
            model_name (str, optional): 要使用的模型名称，默认为 OPENAI_MODEL。
            
        Returns:
            Model: 要使用的模型实例。
        """
        logger.info(f"正在使用模型: {model_name or self.model_name or OPENAI_MODEL}")
        return OpenAIChatCompletionsModel(
            model=model_name or self.model_name or OPENAI_MODEL, openai_client=self.client
        )


# 全局单例
# 默认模型提供者
CUSTOM_MODEL_PROVIDER = CustomModelProvider(client=default_client)
# 轻量(快速)模型提供者
LITE_MODEL_PROVIDER = CustomModelProvider(client=lite_client, model_name=LITE_OPENAI_MODEL)