import requests
import os
from dataclasses import dataclass, field
from typing import List, Dict, Any
from src.utils.logger import logger

DEFAULT_WIKI_AUTH_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NTUzNTJjOTY2MDFiMDFmYjFiOTZmOTYiLCJ0ZWFtSWQiOiI2NzZlNjRmOTkwNDg5MTI1MGMyMzhlMjciLCJ0bWJJZCI6IjY3NmU2NGY5OTA0ODkxMjUwYzIzOGUyZiIsImlzUm9vdCI6dHJ1ZSwiZXhwIjoxNzQ4OTMyMTYxLCJpYXQiOjE3NDgzMjczNjF9.j1nm3vF7OvnimOHHV2X5I7IpJkjQucM2bs_-sKof6Sc"
DEFAULT_WIKI_AUTH_API_KEY="fastgpt-eWAS92I7inR5O6BsXS1zghX1lxrJfvWVoEWuUJSn2qlx3XBWiQFW2dbqQnuH8xh4S"
DEFAULT_WIKI_DATASET_ID="67b5ae04e4606cabc1106c47"

# 定义表示搜索结果中分数项的数据类
@dataclass
class ScoreItem:
    type: str
    value: float
    index: int

# 定义表示单个搜索结果项的数据类
@dataclass
class SearchResultItem:
    id: str
    updateTime: str
    q: str  # 文档原文片段
    a: str  # 文档答案片段 (如果存在)
    chunkIndex: int
    datasetId: str
    collectionId: str
    sourceId: str
    sourceName: str  # 文档来源名称
    score: List[ScoreItem]  # 分数列表
    tokens: int

def search_docs_by_text(
    text: str,
    limit: int = 5000,
) -> List[SearchResultItem]:
    """
    通过文本搜索文档知识库。
    提供了一个结构化查询接口，用于从知识库中获取与“乳制品”“奶油”“奶油芝士”“植物奶”等烘焙和餐饮原材料相关的专业知识。
    知识内容包括但不限于产品产地、成分类型、储存方式、保质期、使用建议、口感描述、适用场景和常见问题。
    【重要,一定要遵守】当你使用了该工具后，请在回复中以（sourceName, datasetId）的形式告知用户你使用了该工具，并告知哪些部分是来自于该工具的查询结果。
    比如：引用自【乳制品 李寻之 2.0（1）.pdf】，数据集ID：67b5ae04e4606cabc1106c47

    Args:
        text: 搜索文本，例如："日清山茶花适合什么甜品使用？"，"植物奶和动物奶油的区别是什么？"，"安佳淡奶油使用方法"
        limit: 返回结果的最大文本长度，默认为 5000字符。

    Returns:
        List[SearchResultItem]: 搜索结果数据列表，如果请求失败则返回空列表。
    """

    dataset_id = os.getenv("WIKI_DATASET_ID", DEFAULT_WIKI_DATASET_ID)
    token = os.getenv("WIKI_AUTH_TOKEN", DEFAULT_WIKI_AUTH_TOKEN)
    wiki_auth_api_key = os.getenv("WIKI_AUTH_API_KEY", DEFAULT_WIKI_AUTH_API_KEY)
    
    
    if not dataset_id or not token:
        logger.error("错误: 未找到必要的环境变量 WIKI_DATASET_ID 或 WIKI_AUTH_TOKEN");
        return []

    url = "https://pdftest.summerfarm.net/api/core/dataset/searchTest"
    headers = {
        # 'Cookie': f'fastgpt_token={token}',
        'Authorization': f'Bearer {wiki_auth_api_key}',
        'Content-Type': 'application/json'
    }
    payload = {
        "datasetId": dataset_id,
        "text": text,
        "searchMode": "mixedRecall",  # 使用混合召回模式
        "usingReRank": False,  # 不使用重排序
        "limit": limit,
        "similarity": 0,  # 相似度阈值设为0
        "datasetSearchUsingExtensionQuery": False,  # 不使用扩展查询
        "datasetSearchExtensionModel": "",  # 扩展模型为空
        "datasetSearchExtensionBg": ""  # 扩展背景为空
    }

    logger.info(f"search_docs_by_text: {text}")
    logger.info(f"search_docs_by_text: {url}")
    logger.info(f"search_docs_by_text: {headers}")
    logger.info(f"search_docs_by_text: {payload}")

    try:
        # 发起 POST 请求
        response = requests.post(url, headers=headers, json=payload)
        
        # 解析 JSON 响应
        logger.info(f"search_docs_by_text response: {response.text[0:100]}...")
        # logger.info(f"search_docs_by_text response: {response.text}...")
        
        response.raise_for_status()
        response_data = response.json()
        
        if response_data.get('code') != 200:
            logger.error(f"API 返回错误: {response_data.get('message')}")
            return []

        # 将字典转换为数据类实例，确保包含所有必需字段
        search_data_list = []
        raw_results = response_data.get('data', {}).get('list', [])
        if raw_results: # Check if list is not empty or None
            for item_dict in raw_results:
                # Ensure 'tokens' field exists, default to 0
                item_dict.setdefault('tokens', 0)

                # Process the 'score' field: convert list of dicts to list of ScoreItem objects
                score_data = item_dict.get('score', [])
                processed_scores = []
                if isinstance(score_data, list):
                    for s_item in score_data:
                        if isinstance(s_item, dict):
                            try:
                                processed_scores.append(ScoreItem(**s_item))
                            except TypeError as score_te:
                                logger.warning(f"Skipping score item due to TypeError: {score_te}. Score item data: {s_item}")
                        else:
                            logger.warning(f"Skipping score item because it's not a dictionary: {s_item}")
                else:
                    logger.warning(f"Score data is not a list: {score_data}")

                # Create SearchResultItem instance with known fields
                try:
                    result_item = SearchResultItem(
                        id=item_dict.get('id'),
                        updateTime=item_dict.get('updateTime'),
                        q=item_dict.get('q'),
                        a=item_dict.get('a'),
                        chunkIndex=item_dict.get('chunkIndex'),
                        datasetId=item_dict.get('datasetId'),
                        collectionId=item_dict.get('collectionId'),
                        sourceId=item_dict.get('sourceId'),
                        sourceName=item_dict.get('sourceName'),
                        score=processed_scores,
                        tokens=item_dict.get('tokens')
                    )
                    search_data_list.append(result_item)
                except TypeError as te:
                    logger.error(f"Failed to create SearchResultItem for ID {item_dict.get('id', 'N/A')} due to TypeError: {te}. Item data: {item_dict}")
                except Exception as ex:
                    logger.error(f"Unexpected error creating SearchResultItem for ID {item_dict.get('id', 'N/A')}: {ex}. Item data: {item_dict}")
            
        return search_data_list

    except requests.exceptions.RequestException as e:
        logger.exception(f"请求失败: {e}")
        return []
    except Exception as e:
        logger.exception(f"处理响应失败: {e}")
        return []
    
from src.services.agent.tools.tool_manager import tool_manager
tool_manager.register_as_function_tool(search_docs_by_text)
